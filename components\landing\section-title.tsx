import { AuroraText } from '../magicui/aurora-text'
import { getDictionary } from '@/lib/i18n/dictionaries'
import type { Locale } from '@/lib/i18n/config'

interface SectionTitleProps {
  title?: string
  description?: string
  id: string
  lang?: Locale
}

export const SectionTitle = async ({
  title,
  description,
  id,
  lang
}: SectionTitleProps) => {
  // 如果传入了 lang，可以在这里获取字典进行翻译
  // 但目前保持原有的 title 和 description 传入方式

  return (
    <div id={id} className="text-center mb-16 scroll-mt-32 anchor">
      {title && (
        <AuroraText className="text-3xl font-bold text-gray-900 sm:text-4xl">
          {title}
        </AuroraText>
      )}
      <p className="mt-4 text-xl text-gray-500">{description}</p>
    </div>
  )
}
