"use client";

import { usePathname, useRouter } from "next/navigation";
import { i18n, type Locale } from "@/lib/i18n/config";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Languages, ChevronDown } from "lucide-react";

interface LanguageSwitcherProps {
  currentLang: Locale;
}

const languageNames = {
  en: "English",
  zh: "中文",
};

export default function LanguageSwitcher({
  currentLang,
}: LanguageSwitcherProps) {
  const pathname = usePathname();
  const router = useRouter();

  const handleLanguageChange = (newLang: Locale) => {
    if (newLang === currentLang) return;

    // Remove the current language from the pathname
    const segments = pathname.split("/");
    if (i18n.locales.includes(segments[1] as Locale)) {
      segments[1] = newLang;
    } else {
      segments.unshift("", newLang);
    }

    const newPath = segments.join("/");
    router.push(newPath);
  };

  return (
    <DropdownMenu modal={false}>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className="w-[100px] h-8 text-sm">
          <Languages className="h-4 w-4 mr-2" />
          {languageNames[currentLang]}
          <ChevronDown className="h-4 w-4 ml-auto" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="z-100">
        {i18n.locales.map((locale) => (
          <DropdownMenuItem
            key={locale}
            onClick={() => handleLanguageChange(locale)}
            className="cursor-pointer"
          >
            {languageNames[locale]}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
