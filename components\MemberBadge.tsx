// components/MemberBadge.tsx
import React from 'react'
import { Crown, Infinity } from 'lucide-react'
import clsx from 'clsx'

type BadgeType = 'annual' | 'lifetime'

interface MemberBadgeProps {
  type: BadgeType
  className?: string
}

export const MemberBadge: React.FC<MemberBadgeProps> = ({ type, className }) => {
  const isLifetime = type === 'lifetime'

  const commonStyles =
    'inline-flex items-center px-2 py-1 rounded-full text-xs font-semibold shadow-sm'

  const badgeStyles = clsx(commonStyles, {
    'bg-yellow-400 text-yellow-900': !isLifetime,
    'bg-gradient-to-r from-[#6F49E0] via-[#308CFE] to-[#FE0D88] text-white':
      isLifetime,
  })

  const icon = isLifetime ? (
    <Infinity
      className="w-4 h-4 mr-1 text-white drop-shadow-[0_0_4px_rgba(255,255,255,0.6)]"
      strokeWidth={2.5}
    />
  ) : (
    <Crown className="w-4 h-4 mr-1 text-yellow-800" />
  )

  return (
    <span className={badgeStyles + ' ' + className}>
      {icon}
      {type.toUpperCase()}
    </span>
  )
}
