'use client'

import { useState } from 'react'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Loader2 } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { toggleSubscriptionAction } from '@/lib/payments/actions'
import type { Dictionary } from '@/types/i18n'

interface SubscriptionToggleProps {
  subscriptionId: string
  initialCancelAtPeriodEnd: boolean
  subscriptionStatus?: string
  className?: string
  dict?: Dictionary['profile']['subscriptionToggle']
}

// Default English translations for backward compatibility
const defaultDict = {
  label: "Auto-renewal",
  description: "Automatically renew subscription",
  cancelledLabel: "Cancelled",
  cancelledDescription: "Will cancel at period end",
  statusCanceled: "Canceled",
  statusActive: "Active",
  statusPastDue: "Past Due",
  statusUnpaid: "Unpaid",
  errorDefault: "Failed to update subscription"
}

export function SubscriptionToggle({
  subscriptionId,
  initialCancelAtPeriodEnd,
  subscriptionStatus,
  className,
  dict
}: SubscriptionToggleProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [cancelAtPeriodEnd, setCancelAtPeriodEnd] = useState(initialCancelAtPeriodEnd)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()

  // Use provided dict or fall back to default
  const t = dict || defaultDict



  // Check if subscription is canceled
  const isCanceled = subscriptionStatus === 'canceled'

  const handleToggle = async () => {
    // Don't allow toggle if subscription is canceled
    if (isCanceled) {
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const result = await toggleSubscriptionAction(subscriptionId)

      if (!result.success) {
        throw new Error(result.error || t.errorDefault)
      }

      // Update local state
      setCancelAtPeriodEnd(result.subscription?.cancelAtPeriodEnd ?? false)

      // Refresh the page to update all subscription info
      router.refresh()
    } catch (error) {
      console.error('Error toggling subscription:', error)
      setError(error instanceof Error ? error.message : t.errorDefault)
      // Revert the switch state on error
      setCancelAtPeriodEnd(cancelAtPeriodEnd)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <Switch
        id={`subscription-toggle-${subscriptionId}`}
        checked={!cancelAtPeriodEnd && !isCanceled}
        onCheckedChange={handleToggle}
        disabled={isLoading || isCanceled}
      />
      <div className="grid gap-1.5 leading-none">
        <Label
          htmlFor={`subscription-toggle-${subscriptionId}`}
          className={`text-sm font-medium leading-none ${
            isCanceled ? 'opacity-50 cursor-not-allowed' : 'peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
          }`}
        >
          {isLoading && <Loader2 className="h-3 w-3 mr-1 animate-spin inline" />}
          {isCanceled
            ? t.statusCanceled
            : cancelAtPeriodEnd
            ? t.cancelledLabel
            : t.label
          }
        </Label>
        <p className="text-xs text-muted-foreground">
          {isCanceled
            ? 'Subscription has been canceled'
            : cancelAtPeriodEnd
            ? t.cancelledDescription
            : t.description
          }
        </p>
        {error && (
          <div className="text-xs text-red-600 dark:text-red-400">
            {error}
          </div>
        )}
      </div>
    </div>
  )
}
