# Blog Module Documentation

## Overview

The blog module provides a complete internationalized (i18n) blog system for Mind Elixir with support for markdown content, multiple languages, and category-based organization.

## Features

- ✅ **Full i18n Support**: English and Chinese language support
- ✅ **Markdown Content**: Write blog posts in markdown format with frontmatter
- ✅ **Category System**: Organize posts by categories (updates, tutorials, tips)
- ✅ **Featured Posts**: Highlight important posts
- ✅ **Tag System**: Tag posts for better organization
- ✅ **Responsive Design**: Mobile-friendly blog layout
- ✅ **SEO Optimized**: Proper meta tags and structured data
- ✅ **Reading Time**: Automatic reading time calculation
- ✅ **Navigation Integration**: Blog link in main navigation
- ✅ **Blog Header**: Dedicated header component with back button and navigation
- ✅ **Professional Typography**: Uses `@tailwindcss/typography` for beautiful text rendering

## File Structure

```
├── app/[lang]/blog/
│   ├── layout.tsx                  # Blog layout with BlogHeader
│   ├── page.tsx                    # Blog listing page
│   └── [slug]/
│       ├── page.tsx                # Individual blog post page
│       └── not-found.tsx           # 404 page for missing posts
├── components/blog/
│   ├── blog-card.tsx               # Blog post card component
│   ├── blog-list.tsx               # Blog listing with filters
│   ├── blog-post.tsx               # Individual blog post component
│   └── blog-header.tsx             # Blog-specific header component
├── content/blog/
│   ├── welcome-to-mind-elixir.en.md
│   ├── welcome-to-mind-elixir.zh.md
│   ├── getting-started-guide.en.md
│   ├── getting-started-guide.zh.md
│   ├── mind-mapping-tips.en.md
│   └── mind-mapping-tips.zh.md
├── lib/blog.ts                     # Blog utility functions
├── types/blog.ts                   # TypeScript type definitions
└── dictionaries/
    ├── en.json                     # English translations (updated)
    └── zh.json                     # Chinese translations (updated)
```

## Usage

### Accessing the Blog

- English: `http://localhost:4000/en/blog`
- Chinese: `http://localhost:4000/zh/blog`

### Creating New Blog Posts

1. Create a new markdown file in `content/blog/` with the naming convention:
   - English: `post-slug.en.md`
   - Chinese: `post-slug.zh.md`

2. Add frontmatter to the top of your markdown file:

```markdown
---
title: "Your Post Title"
description: "Brief description of the post"
date: "2025-05-25"
category: "updates" | "tutorials" | "tips"
readTime: 5
author: "Author Name"
tags: ["tag1", "tag2", "tag3"]
featured: true | false
---

# Your Post Content

Write your blog post content here in markdown format.
```

### Frontmatter Fields

- **title**: Post title (required)
- **description**: Brief description for SEO and cards (required)
- **date**: Publication date in YYYY-MM-DD format (required)
- **category**: One of "updates", "tutorials", or "tips" (required)
- **readTime**: Estimated reading time in minutes (optional, auto-calculated if not provided)
- **author**: Author name (optional)
- **tags**: Array of tags (optional)
- **featured**: Whether to feature this post (optional, defaults to false)

### Supported Markdown Features

- Headers (H1-H6)
- Paragraphs and line breaks
- **Bold** and *italic* text
- Links and images
- Code blocks and inline code
- Lists (ordered and unordered)
- Blockquotes
- Tables

### Categories

The blog supports three categories:

1. **Updates**: Product announcements, releases, and news
2. **Tutorials**: Step-by-step guides and how-to content
3. **Tips**: Quick tips and best practices

### Navigation Integration

The blog is automatically integrated into the main navigation:
- Desktop: Shows as "BLOG" in the header navigation
- Mobile: Available in the dropdown menu
- Links directly to the blog listing page

### Blog Header Component

The blog uses a dedicated header component (`BlogHeader`) that provides:

- **Logo and branding**: Consistent with the main site
- **Navigation links**: Home and Blog navigation
- **Back button**: Conditional back button for blog post pages
- **Language switcher**: Switch between supported languages
- **User avatar**: User authentication status and profile access
- **Mobile responsive**: Dropdown menu for mobile devices

#### BlogHeader Props

```typescript
interface BlogHeaderProps {
  lang: Locale;
  dict: Dictionary;
  showBackButton?: boolean;  // Show back button (default: false)
  backHref?: string;         // Back button destination (default: /${lang}/blog)
}
```

#### Usage Examples

```tsx
// Blog listing page (no back button)
<BlogHeader lang={lang} dict={dict} />

// Blog post page (with back button)
<BlogHeader
  lang={lang}
  dict={dict}
  showBackButton={true}
  backHref={`/${lang}/blog`}
/>
```

### Styling

Blog posts use the `@tailwindcss/typography` plugin for professional typography:
- **Typography Plugin**: Uses official Tailwind CSS typography plugin
- **Theme Integration**: Respects the site's dark/light theme with `prose-invert`
- **Customization**: Custom prose modifiers for colors and styling
- **Responsive Design**: Automatically responsive typography scaling
- **Code Highlighting**: Built-in code block and inline code styling

#### Typography Classes Used

```tsx
className="prose prose-lg dark:prose-invert max-w-none prose-headings:font-bold prose-a:text-primary prose-a:no-underline hover:prose-a:underline prose-code:text-primary prose-code:bg-muted prose-pre:bg-muted prose-pre:border prose-blockquote:border-l-primary"
```

#### Key Features

- `prose prose-lg`: Base typography with large text size
- `dark:prose-invert`: Dark mode support
- `max-w-none`: Remove default max-width constraint
- Custom color modifiers for links, code, and blockquotes

### SEO Features

- Automatic meta tags generation
- Open Graph tags for social sharing
- Twitter Card support
- Structured data for search engines
- Language-specific URLs

## Development

### Adding New Languages

To add support for a new language:

1. Update `lib/i18n/config.ts` to include the new locale
2. Add translations to `dictionaries/[locale].json`
3. Update the `Dictionary` interface in `types/i18n.ts`
4. Create blog posts with the new language suffix: `post-slug.[locale].md`

### Customizing Styles

The blog uses `@tailwindcss/typography` plugin configured in `app/globals.css`:

```css
@plugin '@tailwindcss/typography';
```

You can customize typography by:

- **Prose Modifiers**: Use Tailwind's prose modifier classes
- **CSS Variables**: Modify color variables in the theme
- **Custom Classes**: Add custom styles in `app/globals.css`
- **Plugin Configuration**: Extend the typography plugin (Tailwind CSS v4)

#### Example Customizations

```tsx
// Custom prose styling
<div className="prose prose-xl dark:prose-invert prose-headings:text-blue-600 prose-a:text-green-600">
  {content}
</div>
```

### Adding New Categories

To add a new category:

1. Update the `category` type in `types/blog.ts`
2. Add the category translation to both language dictionaries
3. Update the category colors in `blog-card.tsx` and `blog-post.tsx`

## API Reference

### Blog Utility Functions

```typescript
// Get all posts for a specific language
getAllPosts(lang: Locale): Promise<BlogPostMeta[]>

// Get a specific post by slug and language
getPostBySlug(slug: string, lang: Locale): Promise<BlogPost | null>

// Get all available slugs (for static generation)
getAllSlugs(): string[]

// Get posts by category
getPostsByCategory(category: string, lang: Locale): Promise<BlogPostMeta[]>

// Get featured posts
getFeaturedPosts(lang: Locale): Promise<BlogPostMeta[]>

// Format date according to locale
formatDate(dateString: string, lang: Locale): string
```

## Troubleshooting

### Common Issues

1. **Blog posts not showing**: Ensure markdown files are in the correct format with proper frontmatter
2. **Images not loading**: Place images in the `public` folder and reference them with absolute paths
3. **Styling issues**: Check that the prose classes are properly applied in the blog post component
4. **Build errors**: Verify that all required frontmatter fields are present in your markdown files
5. **"Module not found: Can't resolve 'fs'" error**: This was resolved by:
   - Adding `'server-only'` import to `lib/blog.ts`
   - Moving `formatDate` function to `lib/date-utils.ts` for client-side use
   - Ensuring server-side functions are only used in server components

### Performance Considerations

- Blog posts are statically generated at build time
- Images should be optimized before adding to the blog
- Large markdown files may impact build time

## Future Enhancements

Potential improvements for the blog module:

- [ ] Search functionality
- [ ] RSS feed generation
- [ ] Comment system integration
- [ ] Related posts suggestions
- [ ] Author pages
- [ ] Archive pages by date
- [ ] Blog post series support
- [ ] Social sharing buttons
- [ ] Reading progress indicator
