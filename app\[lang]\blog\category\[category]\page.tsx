import { Metadata } from 'next';
import { getDictionary } from '@/lib/i18n/dictionaries';
import { getAllPosts } from '@/lib/blog';
import { BlogList } from '@/components/blog/blog-list';
import type { PageProps } from '@/types/i18n';
import type { Locale } from '@/lib/i18n/config';

interface BlogCategoryPageProps {
  params: Promise<{
    lang: Locale;
    category: string;
  }>;
}

// Generate static params for all categories and languages
export async function generateStaticParams() {
  const categories = ['all', 'updates', 'tutorials', 'tips'];
  const locales: Locale[] = ['en', 'zh'];

  const params = [];
  for (const category of categories) {
    for (const lang of locales) {
      params.push({ category, lang });
    }
  }

  return params;
}

export async function generateMetadata({ params }: BlogCategoryPageProps): Promise<Metadata> {
  const { lang, category } = await params;
  const dict = await getDictionary(lang);

  const categoryTitle = category === 'all'
    ? dict.blog.meta.title
    : `${dict.blog.categories[category as keyof typeof dict.blog.categories]} - ${dict.blog.meta.title}`;

  return {
    title: categoryTitle,
    description: dict.blog.meta.description,
    openGraph: {
      title: categoryTitle,
      description: dict.blog.meta.description,
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: categoryTitle,
      description: dict.blog.meta.description,
    },
  };
}

export default async function BlogCategoryPage({ params }: BlogCategoryPageProps) {
  const { lang, category } = await params;
  const dict = await getDictionary(lang);
  const allPosts = await getAllPosts(lang);

  // Filter posts by category
  const filteredPosts = category === 'all'
    ? allPosts
    : allPosts.filter(post => post.category === category);

  return (
    <div className="container mx-auto px-4 py-12">
      {/* Page Header */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">
          {category === 'all'
            ? dict.blog.title
            : `${dict.blog.categories[category as keyof typeof dict.blog.categories]} - ${dict.blog.title}`
          }
        </h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          {dict.blog.description}
        </p>
      </div>

      {/* Blog List */}
      <BlogList posts={filteredPosts} lang={lang} dict={dict} selectedCategory={category} />
    </div>
  );
}
