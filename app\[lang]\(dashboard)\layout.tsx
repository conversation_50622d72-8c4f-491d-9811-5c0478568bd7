import { getDictionary } from '@/lib/i18n/dictionaries'
import type { LayoutProps } from '@/types/i18n'
import DashboardHeader from '@/components/DashboardHeader'



export default async function Layout({ children, params }: LayoutProps) {
  const { lang } = await params
  const dict = await getDictionary(lang)

  return (
    <section className="flex flex-col min-h-screen">
      <DashboardHeader lang={lang} dict={dict} />
      {children}
    </section>
  )
}
