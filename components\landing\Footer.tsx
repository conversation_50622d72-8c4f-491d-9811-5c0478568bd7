import { GitHubLogoIcon } from "@radix-ui/react-icons";
import { Mail, Copyright } from "lucide-react";
import Link from "next/link";
import { getDictionary } from '@/lib/i18n/dictionaries'
import type { Locale } from '@/lib/i18n/config'

interface FooterProps {
  lang: Locale
}

export const Footer = async ({ lang }: FooterProps) => {
  const dict = await getDictionary(lang)
  return (
    <footer className="bg-gray-50 dark:bg-darkblue py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div>
            <h3 className="text-lg font-semibold  mb-4">{dict.footer.appName}</h3>
            <p className="text-gray-600">
              {dict.heroSection.title.line1 + ' ' + dict.heroSection.title.line2}
            </p>
          </div>

          <div className="grid grid-cols-2 gap-8">
            <div>
              <h4 className="text-sm font-semibold text-gray-600 mb-3">{dict.footer.sections.product}</h4>
              <ul className="space-y-2">
                {dict.footer.navigation.product.map((link) => (
                  <li key={link.name}>
                    <a
                      href={`/${lang}${link.href}`}
                      className="text-gray-500 hover:text-gray-600 transition-colors"
                    >
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
            <div>
              <h4 className="text-sm font-semibold text-gray-600 mb-3">{dict.footer.sections.feedback}</h4>
              <ul className="space-y-2">
                {dict.footer.navigation.feedback.map((link) => (
                  <li key={link.name}>
                    <a
                      href={link.href}
                      className="text-gray-500 hover:text-gray-600 transition-colors flex items-center gap-2"
                      target={link.href.startsWith("http") ? "_blank" : undefined}
                      rel={link.href.startsWith("http") ? "noopener noreferrer" : undefined}
                    >
                      {link.href.includes("mailto") ? (
                        <Mail className="h-4 w-4" />
                      ) : (
                        <GitHubLogoIcon className="h-4 w-4" />
                      )}
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
            <div>
              <h4 className="text-sm font-semibold text-gray-600 mb-3">{dict.footer.sections.legal}</h4>
              <ul className="space-y-2">
                {dict.footer.navigation.legal.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-gray-500 hover:text-gray-600 transition-colors"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          <div>
            <h4 className="text-sm font-semibold text-gray-600 mb-3">{dict.footer.sections.followUs}</h4>
            <div className="flex space-x-4">
              {dict.footer.socialLinks.map((social) => (
                <a
                  key={social.href}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-500 hover:text-gray-600 transition-colors"
                  aria-label={social.label}
                >
                  {social.href.includes("github") ? (
                    <GitHubLogoIcon className="h-6 w-6" />
                  ) : (
                    <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                    </svg>
                  )}
                </a>
              ))}
            </div>
          </div>
        </div>

        <div className="mt-12 border-t border-gray-200 dark:border-gray-700 pt-8 flex items-center justify-between">
          <div className="flex items-center text-gray-500">
            <Copyright className="h-4 w-4 mr-2" />
            <span>{new Date().getFullYear()} {dict.footer.appName}. {dict.footer.copyright}</span>
          </div>
          <div className="flex items-center">
            <a href="https://turbo0.com/item/mind-elixir-desktop" target="_blank" rel="noopener noreferrer">
              <img src="https://img.turbo0.com/badge-listed-light.svg" alt="Listed on Turbo0" style={{height: '54px', width: 'auto'}} />
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
};