import { Badge } from '@/components/ui/badge';
import { Calendar, Clock } from 'lucide-react';
import type { BlogPost } from '@/types/blog';
import type { Locale } from '@/lib/i18n/config';
import type { Dictionary } from '@/types/i18n';
import { formatDate } from '@/lib/date-utils';

interface BlogPostProps {
  post: BlogPost;
  lang: Locale;
  dict: Dictionary;
}

export function BlogPostComponent({ post, lang, dict }: BlogPostProps) {
  const categoryColors = {
    updates: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
    tutorials: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    tips: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
  };

  return (
    <article className="max-w-4xl mx-auto">

      {/* Article Header */}
      <header className="mb-8">
        <div className="flex items-center space-x-4 mb-4">
          <Badge
            variant="secondary"
            className={categoryColors[post.category]}
          >
            {dict.blog.categories[post.category]}
          </Badge>
          {post.featured && (
            <Badge variant="default" className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300">
              Featured
            </Badge>
          )}
        </div>

        <h1 className="text-4xl font-bold mb-4 leading-tight">
          {post.title}
        </h1>

        <p className="text-xl text-muted-foreground mb-6">
          {post.description}
        </p>

        <div className="flex items-center space-x-6 text-sm text-muted-foreground">
          <div className="flex items-center space-x-2">
            <Calendar className="h-4 w-4" />
            <span>{dict.blog.publishedOn} {formatDate(post.date, lang)}</span>
          </div>
          <div className="flex items-center space-x-2">
            <Clock className="h-4 w-4" />
            <span>{post.readTime} {dict.blog.readTime}</span>
          </div>
          {post.author && (
            <div>
              <span>{post.author}</span>
            </div>
          )}
        </div>

        {post.tags && post.tags.length > 0 && (
          <div className="mt-4 flex flex-wrap gap-2">
            {post.tags.map((tag) => (
              <Badge key={tag} variant="outline" className="text-sm">
                {tag}
              </Badge>
            ))}
          </div>
        )}
      </header>

      {/* Article Content */}
      <div
        className="prose prose-lg dark:prose-invert max-w-none prose-headings:font-bold prose-a:text-primary prose-a:no-underline hover:prose-a:underline prose-code:text-primary prose-code:bg-muted prose-pre:bg-muted prose-pre:border prose-blockquote:border-l-primary"
        dangerouslySetInnerHTML={{ __html: post.content }}
      />


    </article>
  );
}
