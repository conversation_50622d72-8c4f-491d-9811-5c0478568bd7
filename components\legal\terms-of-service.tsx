import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';

export function TermsOfService() {
  const sections = [
    {
      title: "Introduction",
      content: (
        <>
          <p className="mb-4">
            These Terms of Service ("Terms") govern your use of the Mind Elixir Desktop application ("the Application") provided by Mind Elixir ("we," "us," or "our").
          </p>
          <p className="mb-4">
            By downloading, installing, or using the Application, you agree to be bound by these Terms. If you disagree with any part of the Terms, you may not use the Application.
          </p>
        </>
      )
    },
    {
      title: "License",
      content: (
        <>
          <p className="mb-4">
            Subject to these Terms, we grant you a limited, non-exclusive, non-transferable, revocable license to download, install, and use the Application for your personal or business purposes.
          </p>
          <p className="mb-4">
            This license does not include any resale or commercial use of the Application or its contents; any collection and use of any product listings, descriptions, or prices; any derivative use of the Application or its contents; or any use of data mining, robots, or similar data gathering and extraction tools.
          </p>
        </>
      )
    },
    {
      title: "User Accounts and Authentication",
      content: (
        <>
          <p className="mb-4">
            Some features of the Application may require you to create an account. You are responsible for maintaining the confidentiality of your account and password and for restricting access to your computer or device. You agree to accept responsibility for all activities that occur under your account.
          </p>
          <p className="mb-4">
            You must provide accurate and complete information when creating an account and keep your account information updated. You are solely responsible for the activity that occurs on your account.
          </p>
          <p className="mb-4">
            <strong>Third-Party Authentication:</strong> Our Application offers the option to sign in using third-party authentication providers such as Google, GitHub, and other social media platforms. By using these services to authenticate:
          </p>
          <ul className="list-disc pl-6 mb-4">
            <li>You authorize us to access and use certain information from your third-party account as permitted by that service</li>
            <li>You understand that your relationship with the third-party service provider is governed solely by your agreement with that provider</li>
            <li>You acknowledge that the third-party service may change its policies and restrictions at any time</li>
            <li>You agree that we shall not be liable for any loss or damage that may arise from your use of third-party authentication services</li>
          </ul>
          <p className="mb-4">
            If you choose to disconnect a third-party authentication service from our Application, this may affect your ability to access certain features of the Application that rely on that connection.
          </p>
        </>
      )
    },
    {
      title: "Subscription and Payments",
      content: (
        <>
          <p className="mb-4">
            The Application offers a free basic plan and two premium options: an Annual Subscription and a Lifetime License. By selecting a premium plan, you agree to pay the subscription or license fees indicated for that service.
          </p>
          <p className="mb-4">
            <strong>Annual Subscription:</strong> Annual subscription fees are billed in advance on a yearly basis. You can cancel your subscription at any time, but no refunds will be provided for any unused portion of the current billing period. Your subscription will automatically renew unless you cancel it before the renewal date.
          </p>
          <p className="mb-4">
            <strong>Lifetime License:</strong> The Lifetime License is a one-time payment that grants you perpetual access to premium features for the current major version of the Application and all its updates. This license is non-transferable and applies only to the account that purchased it.
          </p>
          <p className="mb-4">
            We reserve the right to change our subscription plans or adjust pricing for our service in any manner and at any time as we may determine in our sole discretion. Any price changes will take effect following notice to you. Price changes will not affect existing Lifetime License holders.
          </p>
        </>
      )
    },
    {
      title: "Intellectual Property",
      content: (
        <>
          <p className="mb-4">
            The Application, including its original content, features, and functionality, is owned by Mind Elixir and is protected by international copyright, trademark, patent, trade secret, and other intellectual property or proprietary rights laws.
          </p>
          <p className="mb-4">
            You may not modify, reproduce, distribute, create derivative works or adaptations of, publicly display or in any way exploit the Application in whole or in part except as expressly authorized by us.
          </p>
          <p className="mb-4">
            Mind Elixir's name, logo, and all related names, logos, product and service names, designs, and slogans are trademarks of Mind Elixir or its affiliates. You may not use such marks without our prior written permission.
          </p>
        </>
      )
    },
    {
      title: "Prohibited Uses",
      content: (
        <>
          <p className="mb-4">
            You agree not to use the Application:
          </p>
          <ul className="list-disc pl-6 mb-4">
            <li>In any way that violates any applicable federal, state, local, or international law or regulation</li>
            <li>To transmit, or procure the sending of, any advertising or promotional material, including any "junk mail," "chain letter," "spam," or any other similar solicitation</li>
            <li>To impersonate or attempt to impersonate Mind Elixir, a Mind Elixir employee, another user, or any other person or entity</li>
            <li>To engage in any other conduct that restricts or inhibits anyone's use or enjoyment of the Application, or which may harm Mind Elixir or users of the Application</li>
          </ul>
        </>
      )
    },
    {
      title: "Disclaimer of Warranties",
      content: (
        <>
          <p className="mb-4">
            THE APPLICATION IS PROVIDED "AS IS" AND "AS AVAILABLE" WITHOUT ANY WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, OR NON-INFRINGEMENT.
          </p>
          <p className="mb-4">
            We do not guarantee that the Application will always be available, uninterrupted, timely, secure, or error-free. You understand that you download and use the Application at your own discretion and risk.
          </p>
        </>
      )
    },
    {
      title: "Limitation of Liability",
      content: (
        <>
          <p className="mb-4">
            TO THE MAXIMUM EXTENT PERMITTED BY LAW, IN NO EVENT SHALL MIND ELIXIR, ITS AFFILIATES, OR THEIR LICENSORS, SERVICE PROVIDERS, EMPLOYEES, AGENTS, OFFICERS, OR DIRECTORS BE LIABLE FOR DAMAGES OF ANY KIND, UNDER ANY LEGAL THEORY, ARISING OUT OF OR IN CONNECTION WITH YOUR USE OF THE APPLICATION.
          </p>
          <p className="mb-4">
            THIS INCLUDES ANY DIRECT, INDIRECT, SPECIAL, INCIDENTAL, CONSEQUENTIAL, OR PUNITIVE DAMAGES, INCLUDING BUT NOT LIMITED TO, PERSONAL INJURY, PAIN AND SUFFERING, EMOTIONAL DISTRESS, LOSS OF REVENUE, LOSS OF PROFITS, LOSS OF BUSINESS OR ANTICIPATED SAVINGS, LOSS OF USE, LOSS OF GOODWILL, LOSS OF DATA, AND WHETHER CAUSED BY TORT (INCLUDING NEGLIGENCE), BREACH OF CONTRACT, OR OTHERWISE, EVEN IF FORESEEABLE.
          </p>
        </>
      )
    },
    {
      title: "Changes to Terms",
      content: (
        <>
          <p className="mb-4">
            We reserve the right to modify these Terms at any time. If we make changes to these Terms, we will provide notice of such changes, such as by sending an email notification, providing notice through the Application, or updating the "Last Updated" date at the beginning of these Terms.
          </p>
          <p className="mb-4">
            Your continued use of the Application following the posting of revised Terms means that you accept and agree to the changes.
          </p>
        </>
      )
    },
    {
      title: "Governing Law",
      content: (
        <>
          <p className="mb-4">
            These Terms shall be governed by and construed in accordance with the laws of [Jurisdiction], without regard to its conflict of law provisions.
          </p>
          <p className="mb-4">
            Any legal suit, action, or proceeding arising out of, or related to, these Terms or the Application shall be instituted exclusively in the courts of [Jurisdiction].
          </p>
        </>
      )
    },
    {
      title: "Contact Us",
      content: (
        <>
          <p className="mb-4">
            If you have any questions about these Terms, please contact us at:
          </p>
          <p className="mb-4">
            <a href="mailto:<EMAIL>" className="text-primary hover:underline">
              <EMAIL>
            </a>
          </p>
        </>
      )
    }
  ];

  return (
    <Card className="border-none shadow-none">
      <CardHeader>
        <CardTitle className="text-3xl font-bold">Terms of Service</CardTitle>
        <CardDescription>Last Updated: {new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-8">
        {sections.map((section, index) => (
          <div key={index} className="space-y-4">
            <h2 className="text-xl font-semibold">{section.title}</h2>
            <div className="text-muted-foreground">{section.content}</div>
            {index < sections.length - 1 && <Separator />}
          </div>
        ))}
      </CardContent>
    </Card>
  );
}
