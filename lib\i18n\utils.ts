import type { Locale } from './config';

export function getLocalizedPath(path: string, locale: Locale): string {
  // Remove leading slash if present
  const cleanPath = path.startsWith('/') ? path.slice(1) : path;
  
  // If path is empty (root), just return the locale
  if (!cleanPath) {
    return `/${locale}`;
  }
  
  return `/${locale}/${cleanPath}`;
}

export function removeLocaleFromPath(path: string): string {
  const segments = path.split('/');
  
  // If the first segment (after empty string) is a locale, remove it
  if (segments.length > 1 && ['en', 'zh'].includes(segments[1])) {
    segments.splice(1, 1);
  }
  
  return segments.join('/') || '/';
}

export function getLocaleFromPath(path: string): Locale | null {
  const segments = path.split('/');
  
  if (segments.length > 1 && ['en', 'zh'].includes(segments[1])) {
    return segments[1] as Locale;
  }
  
  return null;
}
