import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { SectionTitle } from './section-title'
import { getDictionary } from '@/lib/i18n/dictionaries'
import type { Locale } from '@/lib/i18n/config'

interface AiSectionProps {
  lang: Locale
}

export async function AiSection({ lang }: AiSectionProps) {
  const dict = await getDictionary(lang)
  return (
    <div className="my-26 max-w-[80vw] m-auto">
      <SectionTitle title={dict.aiSection.sectionTitle} id="ai-powered" />
      <Tabs defaultValue="aigc" className="w-full ">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="aigc">{dict.aiSection.tabs.aigc}</TabsTrigger>
          <TabsTrigger value="mcp">{dict.aiSection.tabs.mcp}</TabsTrigger>
        </TabsList>
        <TabsContent value="aigc">
          <video autoPlay loop className="rounded-lg h-[60vh] mx-auto">
            <source src="/aigc.mp4" type="video/mp4" />
            {dict.aiSection.videoAlt.aigc}
          </video>
        </TabsContent>
        <TabsContent value="mcp">
          <video autoPlay loop className="rounded-lg h-[60vh] mx-auto">
            <source src="/mcp.mp4" type="video/mp4" />
            {dict.aiSection.videoAlt.mcp}
          </video>
        </TabsContent>
      </Tabs>
    </div>
  )
}
