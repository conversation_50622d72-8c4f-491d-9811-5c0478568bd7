import { MeUser } from '@/auth'
import clientPromise, { dbName } from '../mongo'

export async function getUser(userId: string): Promise<any | null> {
  const client = await clientPromise
  const collection = client.db(dbName).collection('users')
  const one = await collection.findOne({
    uuid: userId,
  })
  return one
}

export async function upsertUser(userData: MeUser): Promise<any> {
  const client = await clientPromise
  const collection = client.db(dbName).collection('users')

  const { id: uuid, ...updateData } = userData

  const result = await collection.findOneAndUpdate(
    { uuid },
    {
      $set: {
        ...updateData,
        updatedAt: new Date(),
      },
      $setOnInsert: {
        createdAt: new Date(),
      },
    },
    {
      upsert: true,
      returnDocument: 'after',
    }
  )

  if (!result) {
    throw new Error('Failed to upsert user')
  }

  return result
}

export interface DbUser {
  _id: string
  uuid: string
  createdAt: string
  email: string
  image: string
  name: string
  updatedAt: string
  stripeCustomerId?: string
  type?: 'lifetime' | 'annual'
  exp?: Date | null
  cancelAtPeriodEnd?: boolean
}

export async function updateUserStripeCustomerId(
  userId: string,
  stripeCustomerId: string
): Promise<any> {
  const client = await clientPromise
  const collection = client.db(dbName).collection('users')

  const result = await collection.findOneAndUpdate(
    { uuid: userId },
    {
      $set: {
        stripeCustomerId,
        updatedAt: new Date(),
      },
    },
    { returnDocument: 'after' }
  )

  if (!result) {
    throw new Error('Failed to update user stripeCustomerId')
  }

  return result
}

export async function updateUserPayType(
  userId: string,
  type: 'lifetime' | 'annual' | null
): Promise<any> {
  const client = await clientPromise
  const collection = client.db(dbName).collection('users')

  let updateData: any

  if (type === null) {
    // When cancelling subscription, clear type and exp fields
    updateData = {
      $set: {
        updatedAt: new Date()
      },
      $unset: {
        type: '',
        exp: '',
        cancelAtPeriodEnd: ''
      }
    }
  } else {
    updateData = {
      $set: {
        updatedAt: new Date(),
        type: type,
        exp: type === 'lifetime'
          ? null
          : new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
        cancelAtPeriodEnd: false
      }
    }
  }

  const result = await collection.findOneAndUpdate(
    { uuid: userId },
    updateData
  )

  if (!result) {
    throw new Error('Failed to update user type')
  }

  return result
}

export async function updateUserCancelAtPeriodEnd(
  userId: string,
  cancelAtPeriodEnd: boolean
): Promise<any> {
  const client = await clientPromise
  const collection = client.db(dbName).collection('users')

  const result = await collection.findOneAndUpdate(
    { uuid: userId },
    {
      $set: {
        cancelAtPeriodEnd,
        updatedAt: new Date(),
      },
    },
    { returnDocument: 'after' }
  )

  if (!result) {
    throw new Error('Failed to update user cancelAtPeriodEnd status')
  }

  return result
}