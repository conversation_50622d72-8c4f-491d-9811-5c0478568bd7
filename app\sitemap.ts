import type { MetadataRoute } from "next";
import { getAllSlugs } from "@/lib/blog";
import { i18n } from "@/lib/i18n/config";

// 网站基础URL
const BASE_URL = "https://desktop.mind-elixir.com";

// 静态页面配置
const staticPages = [
  {
    path: "",
    priority: 1.0,
    changeFrequency: "weekly" as const,
  },
  {
    path: "/pricing",
    priority: 0.8,
    changeFrequency: "weekly" as const,
  },
  {
    path: "/profile",
    priority: 0.6,
    changeFrequency: "monthly" as const,
  },
  {
    path: "/sign-in",
    priority: 0.5,
    changeFrequency: "monthly" as const,
  },
];

// 博客分类
const blogCategories = ["all", "updates", "tutorials", "tips"];

// 法律页面（无语言前缀）
const legalPages = [
  {
    path: "/privacy",
    priority: 0.3,
    changeFrequency: "yearly" as const,
  },
  {
    path: "/terms",
    priority: 0.3,
    changeFrequency: "yearly" as const,
  },
];

export default function sitemap(): MetadataRoute.Sitemap {
  const currentDate = new Date();
  const urls: MetadataRoute.Sitemap = [];

  urls.push({
    url: `${BASE_URL}/`,
    lastModified: currentDate,
    changeFrequency: "weekly",
    priority: 1.0,
  });

  // 添加法律页面（无语言前缀）
  legalPages.forEach((page) => {
    urls.push({
      url: `${BASE_URL}${page.path}`,
      lastModified: currentDate,
      changeFrequency: page.changeFrequency,
      priority: page.priority,
    });
  });

  // 添加多语言静态页面
  i18n.locales.forEach((locale) => {
    staticPages.forEach((page) => {
      const url =
        page.path === ""
          ? `${BASE_URL}/${locale}`
          : `${BASE_URL}/${locale}${page.path}`;

      urls.push({
        url,
        lastModified: currentDate,
        changeFrequency: page.changeFrequency,
        priority: page.priority,
      });
    });
  });

  // 添加博客分类页面
  i18n.locales.forEach((locale) => {
    blogCategories.forEach((category) => {
      urls.push({
        url: `${BASE_URL}/${locale}/blog/category/${category}`,
        lastModified: currentDate,
        changeFrequency: "weekly",
        priority: 0.7,
      });
    });
  });

  // 添加博客文章页面
  const blogSlugs = getAllSlugs();
  i18n.locales.forEach((locale) => {
    blogSlugs.forEach((slug) => {
      urls.push({
        url: `${BASE_URL}/${locale}/blog/${slug}`,
        lastModified: currentDate,
        changeFrequency: "monthly",
        priority: 0.6,
        // alternates: {
        //   languages: Object.fromEntries(
        //     i18n.locales.map((altLocale) => [
        //       altLocale,
        //       `${BASE_URL}/${altLocale}/blog/${slug}`,
        //     ])
        //   ),
        // },
      });
    });
  });

  return urls;
}
