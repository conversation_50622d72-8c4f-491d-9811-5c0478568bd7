'use client'

import Link from 'next/link'
import Image from 'next/image'
import { Menu, ArrowLeft, Home } from 'lucide-react'
import logo from '@/assets/mind-elixir-desktop.svg'
import UserAvatar from '@/components/UserAvatar'
import type { Locale } from '@/lib/i18n/config'
import type { Dictionary } from '@/types/i18n'
import LanguageSwitcher from '@/components/language-switcher'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'

interface BlogHeaderProps {
  lang: Locale
  dict: Dictionary
  showBackButton?: boolean
  backHref?: string
}

export default function BlogHeader({
  lang,
  dict,
  showBackButton = false,
  backHref = `/${lang}/blog/category/all`
}: BlogHeaderProps) {
  const mainLinks = [
    { key: 'home', label: dict.common.back, href: `/${lang}`, icon: Home },
    { key: 'blog', label: dict.blog.title, href: `/${lang}/blog/category/all` },
  ]

  return (
    <header className="border-b fixed w-screen top-0 left-0 z-100 bg-white dark:bg-darkblue">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex justify-between items-center text-gray-900 dark:text-white">
        {/* Left side - Logo and Back Button */}
        <div className="flex items-center gap-4">
          {showBackButton && (
            <Link href={backHref}>
              <Button variant="ghost" size="sm" className="text-[#d0d1df] hover:text-gray-400">
                <ArrowLeft className="h-4 w-4 mr-2" />
                {dict.blog.backToBlog}
              </Button>
            </Link>
          )}

          <Link href={`/${lang}`} className="flex items-center">
            <Image src={logo} width={24} height={24} alt="MindElixir" />
            <span className="ml-2 text-xl">
              Mind Elixir
              {lang === 'zh' && <span className="hidden md:inline"> 妙意灵溪</span>}
            </span>
          </Link>
        </div>

        <div className="flex gap-3 items-center">
          {/* Desktop Navigation - Hidden on mobile (md:flex) */}
          <div className="hidden md:flex gap-3 items-center">
            {/* Main Navigation */}
            {mainLinks.map((link) => (
              <Link
                key={link.key}
                href={link.href}
                className="hover:text-gray-400 text-[#d0d1df] text-sm flex items-center gap-1"
              >
                {link.icon && <link.icon className="h-4 w-4" />}
                {link.label}
              </Link>
            ))}


          </div>

          {/* Mobile Navigation Menu - Visible only on mobile (md:hidden) */}
          <div className="md:hidden">
            <DropdownMenu modal={false}>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="text-[#d0d1df]">
                  <Menu className="h-5 w-5" />
                  <span className="sr-only">Open menu</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48 z-100">
                {/* Main Links */}
                {mainLinks.map((link) => (
                  <DropdownMenuItem key={link.key} asChild>
                    <Link
                      href={link.href}
                      className="w-full cursor-pointer flex items-center gap-2"
                    >
                      {link.icon && <link.icon className="h-4 w-4" />}
                      {link.label}
                    </Link>
                  </DropdownMenuItem>
                ))}


              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <LanguageSwitcher currentLang={lang} />
        </div>
      </div>
    </header>
  )
}
