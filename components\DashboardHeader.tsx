'use client'

import Link from 'next/link'
import Image from 'next/image'
import { Menu } from 'lucide-react'
import logo from '@/assets/mind-elixir-desktop.svg'
import UserAvatar from '@/components/UserAvatar'
import type { Locale } from '@/lib/i18n/config'
import type { Dictionary } from '@/types/i18n'
import LanguageSwitcher from '@/components/language-switcher'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'

interface DashboardHeaderProps {
  lang: Locale
  dict: Dictionary
}

export default function DashboardHeader({ lang, dict }: DashboardHeaderProps) {
  const links: (keyof typeof dict.navigation)[] = [
    'feature',
    'ai-powered',
    'pricing',
    'download',
    'faq',
    'blog',
  ]

  return (
    <header className="border-b fixed w-screen top-0 left-0 z-100 bg-white dark:bg-darkblue">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex justify-between items-center text-gray-900 dark:text-white">
        <Link href={`/${lang}`} className="flex items-center">
          <Image src={logo} width={24} height={24} alt="MindElixir" />
          <span className="ml-2 text-xl">
            Mind Elixir
            {lang === 'zh' && <span className="hidden md:inline"> 妙意灵溪</span>}
          </span>
        </Link>

        <div className="flex gap-3 items-center">
          {/* Desktop Navigation - Hidden on mobile (md:flex) */}
          <div className="hidden md:flex gap-3 items-center">
            {links.map((link) => (
              <Link
                key={link}
                href={link === 'blog' ? `/${lang}/blog/category/all` : `/${lang}/#${link}`}
                className="hover:text-gray-400 text-[#d0d1df] text-sm"
              >
                {dict.navigation[link] || link.toUpperCase()}
              </Link>
            ))}
          </div>

          {/* Mobile Navigation Menu - Visible only on mobile (md:hidden) */}
          <div className="md:hidden">
            <DropdownMenu modal={false}>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="text-[#d0d1df]">
                  <Menu className="h-5 w-5" />
                  <span className="sr-only">Open menu</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48 z-100">
                {links.map((link) => (
                  <DropdownMenuItem key={link} asChild>
                    <Link
                      href={link === 'blog' ? `/${lang}/blog/category/all` : `/${lang}/#${link}`}
                      className="w-full cursor-pointer"
                    >
                      {dict.navigation[link] || link.toUpperCase()}
                    </Link>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <LanguageSwitcher currentLang={lang} />
          <UserAvatar lang={lang} dict={dict} />
        </div>
      </div>
    </header>
  )
}
