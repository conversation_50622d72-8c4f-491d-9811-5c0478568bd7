import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { i18n } from '@/lib/i18n/config';


function getLocaleFromHeaders(request: NextRequest): string {
  // Get locale from Accept-Language header
  const acceptLanguage = request.headers.get('accept-language');
  if (!acceptLanguage) return i18n.defaultLocale;

  // Simple language detection
  if (acceptLanguage.includes('zh')) return 'zh';
  return 'en';
}

export async function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;

  // Skip legal pages - they don't need language prefixes
  if (pathname.startsWith('/privacy') || pathname.startsWith('/terms')) {
    return NextResponse.next();
  }

  // Skip SEO files - they don't need language prefixes
  if (
    pathname === '/sitemap.xml' ||
    pathname === '/robots.txt' ||
    pathname.startsWith('/sitemap') ||
    pathname === '/manifest.json'
  ) {
    return NextResponse.next();
  }

  // Check if there is any supported locale in the pathname
  const pathnameIsMissingLocale = i18n.locales.every(
    (locale) => !pathname.startsWith(`/${locale}/`) && pathname !== `/${locale}`
  );

  // Redirect if there is no locale
  if (pathnameIsMissingLocale) {
    const locale = getLocaleFromHeaders(request);

    // Special handling for root path
    if (pathname === '/') {
      return NextResponse.redirect(
        new URL(`/${locale}`, request.url)
      );
    }

    return NextResponse.redirect(
      new URL(`/${locale}${pathname.startsWith('/') ? '' : '/'}${pathname}`, request.url)
    );
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    // Skip all internal paths (_next), API routes, static files, media files, SEO files
    '/((?!_next|api|favicon.ico|sitemap|robots.txt|manifest.json|.*\\.(?:png|jpg|jpeg|gif|svg|ico|webp|mp4|webm|avi|mov|wmv|flv|mkv|css|js|woff|woff2|ttf|eot)$).*)',
    // Optional: only run on root (/) URL
    // '/'
  ],
};
