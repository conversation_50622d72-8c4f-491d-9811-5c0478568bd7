import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';

export function PrivacyPolicy() {
  const sections = [
    {
      title: "Introduction",
      content: (
        <>
          <p className="mb-4">
            At Mind Elixir, we take your privacy seriously. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our Mind Elixir Desktop application ("the Application").
          </p>
          <p className="mb-4">
            Please read this Privacy Policy carefully. By using the Application, you agree to the collection and use of information in accordance with this policy.
          </p>
        </>
      )
    },
    {
      title: "Information Collection",
      content: (
        <>
          <p className="mb-4">
            <strong>Local Storage:</strong> Mind Elixir is designed with privacy as a core principle. All your mind maps and related data are stored locally on your device. We do not upload your mind maps to any server without your explicit consent.
          </p>
          <p className="mb-4">
            <strong>AI Features:</strong> When you use our AI-powered features (such as automatic idea generation or mind map updates), the specific content you choose to process with AI is temporarily transmitted to our AI service providers. This data is not stored permanently and is only used to provide the requested AI functionality.
          </p>
        </>
      )
    },
    {
      title: "Data Security",
      content: (
        <>
          <p className="mb-4">
            We implement appropriate technical and organizational measures to protect your personal information against unauthorized or unlawful processing, accidental loss, destruction, or damage.
          </p>
          <p className="mb-4">
            However, please note that no method of transmission over the internet or electronic storage is 100% secure. While we strive to use commercially acceptable means to protect your personal information, we cannot guarantee its absolute security.
          </p>
        </>
      )
    },
    {
      title: "Third-Party Authentication",
      content: (
        <>
          <p className="mb-4">
            Our Application offers the option to sign in using third-party authentication providers such as Google, GitHub, and other social media platforms. When you choose to use these services:
          </p>
          <ul className="list-disc pl-6 mb-4">
            <li>We may receive certain profile information from the authentication provider, such as your name, email address, and profile picture</li>
            <li>We do not receive or store your passwords for these third-party accounts</li>
            <li>The information we receive depends on your privacy settings with the third-party service</li>
            <li>We use this information only for authentication purposes and to personalize your experience</li>
          </ul>
          <p className="mb-4">
            Please note that these third-party services have their own privacy policies that govern how they collect, use, and process your personal information. We encourage you to review their privacy policies before using their services to authenticate with our Application.
          </p>
        </>
      )
    },
    {
      title: "Other Third-Party Services",
      content: (
        <>
          <p className="mb-4">
            Our Application may also use other third-party services that collect information used to identify you. These third-party services include:
          </p>
          <ul className="list-disc pl-6 mb-4">
            <li>AI service providers for AI-powered features</li>
            <li>Payment processors (for premium subscriptions)</li>
          </ul>
          <p className="mb-4">
            Each third-party service has its own privacy policy addressing how they use such information.
          </p>
        </>
      )
    },
    {
      title: "Changes to This Privacy Policy",
      content: (
        <>
          <p className="mb-4">
            We may update our Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page and updating the "Last Updated" date.
          </p>
          <p className="mb-4">
            You are advised to review this Privacy Policy periodically for any changes. Changes to this Privacy Policy are effective when they are posted on this page.
          </p>
        </>
      )
    },
    {
      title: "Contact Us",
      content: (
        <>
          <p className="mb-4">
            If you have any questions or suggestions about our Privacy Policy, do not hesitate to contact us at:
          </p>
          <p className="mb-4">
            <a href="mailto:<EMAIL>" className="text-primary hover:underline">
              <EMAIL>
            </a>
          </p>
        </>
      )
    }
  ];

  return (
    <Card className="border-none shadow-none">
      <CardHeader>
        <CardTitle className="text-3xl font-bold">Privacy Policy</CardTitle>
        <CardDescription>Last Updated: {new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-8">
        {sections.map((section, index) => (
          <div key={index} className="space-y-4">
            <h2 className="text-xl font-semibold">{section.title}</h2>
            <div className="text-muted-foreground">{section.content}</div>
            {index < sections.length - 1 && <Separator />}
          </div>
        ))}
      </CardContent>
    </Card>
  );
}
