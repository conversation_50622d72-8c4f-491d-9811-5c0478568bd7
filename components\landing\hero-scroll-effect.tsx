"use client";
import { useEffect } from "react";

export const HeroScrollEffect = () => {
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      const maxScroll = 500;

      const percent = scrollTop / maxScroll;
      if (percent > 1) return;
      
      // map 0 -> 1 to 1 -> 1.6
      const map = document.querySelector<HTMLDivElement>("#wrapper");
      const hero = document.querySelector<HTMLDivElement>("#hero");
      
      if (map) {
        map.style.transform = `scale(${1 + percent * 0.6}) translateY(-${
          percent * 150
        }px)`;
      }
      
      if (hero) {
        hero.style.transform = `translateY(${-percent * 100}px)`;
        hero.style.opacity = `${1 - percent}`;
      }
    };

    window.addEventListener("scroll", handleScroll);
    
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return null;
};
