---
title: '如何使用模型上下文协议 (MCP)'
description: '深入了解模型上下文协议 (MCP)，学习如何通过标准化接口连接 AI 模型与各种数据源和工具。'
date: '2025-01-25'
category: 'tutorials'
readTime: 8
author: 'Mind Elixir 团队'
tags: ['AI', 'MCP', '协议', '集成', '开发']
featured: false
---

## 什么是 MCP？

MCP 是一种让 AI 助手能够连接和使用各种工具的技术。就像手机可以连接不同的应用程序一样，MCP 让 AI 助手可以连接到不同的软件和服务。

### 为什么要使用 MCP？

想象一下，如果您的 AI 助手只能聊天，但不能帮您：

- 查看文件
- 编辑文档
- 连接其他软件

那就太局限了。MCP 让 AI 助手变得更有用，可以：

- **连接更多工具**：AI 可以使用各种现成的工具和服务
- **保护您的数据**：所有操作都在您的设备上进行，更安全
- **灵活选择**：可以根据需要选择不同的 AI 助手

### MCP 如何工作？

简单来说，MCP 就像一个翻译器：

- **AI 助手**：比如 Claude、ChatGPT 等，想要使用某个工具
- **MCP 连接器**：负责让 AI 助手和工具"对话"
- **各种工具**：比如 Mind Elixir 思维导图、文件管理器等

当您告诉 AI 助手"帮我编辑思维导图"时，MCP 就会帮助 AI 助手连接到 Mind Elixir，完成您的要求。

## 支持 MCP 的常用软件

目前有很多软件都支持 MCP，让您可以轻松连接 AI 助手：

### Claude Desktop

- Claude 的桌面版本
- 最先支持 MCP
- 对 MCP 的支持自然是最好的

### Cherry Studio

- 支持配置几乎所有 AI 服务商
- 千余内置助手
- 多数据源知识库

### Cursor

专门为程序员设计的 AI 编辑器：

- 帮助编写和修改代码
- 适合技术用户使用

更多客户端可以看这里：https://modelcontextprotocol.io/clients

## 在 Mind Elixir 中使用 MCP

Mind Elixir 支持 MCP 连接，让您可以用 AI 助手来帮助制作和编辑思维导图。

### Mind Elixir 的 AI 功能

连接 AI 助手后，您可以：

- **让 AI 看懂您的思维导图**：AI 可以理解您画的思维导图内容
- **用语言编辑思维导图**：直接说"添加一个分支"，AI 就会帮您添加
- **整理思维导图结构**：AI 可以帮您重新排列，让逻辑更清楚
- **获得内容建议**：AI 会根据您已有的内容，建议添加什么新想法

### 如何连接 AI 助手

很简单，只需要三步：

#### 第一步：打开 Mind Elixir

- 进入 Mind Elixir 网站
- 创建或打开一个思维导图

#### 第二步：在 AI 助手中添加连接

- 打开您的 AI 助手（比如 Claude Desktop）
- 添加 Mind Elixir 连接
- 连接地址是：`http://localhost:6595/sse`

#### 第三步：开始使用

连接成功后，您就可以对 AI 说：

- **"帮我添加三个分支：想法、计划、行动"**
- **"这个思维导图可以怎么改进？"**
- **"帮我整理一下这些内容的顺序"**
- **"给我一些关于这个主题的新想法"**

### 使用小贴士

- **保持软件打开**：使用时请保持 Mind Elixir 页面开着
- **说话要清楚**：告诉 AI 具体要做什么，效果会更好
- **一步一步来**：复杂的修改可以分几次完成
- **记得保存**：重要的修改完成后要保存思维导图

通过连接 AI 助手，您的思维导图制作会变得更轻松、更高效！
