import { Download, Monitor, Apple, Terminal } from 'lucide-react'
import { SectionTitle } from './section-title'
import { getDictionary } from '@/lib/i18n/dictionaries'
import type { Locale } from '@/lib/i18n/config'

interface DownloadSectionProps {
  lang: Locale
}

const version = '1.0.3'
const base =
  'https://github.com/SSShooter/Mind-Elixir-Desktop-Release/releases/download/app-v'
export const DownloadSection = async ({ lang }: DownloadSectionProps) => {
  const dict = await getDictionary(lang)

  const platforms = [
    {
      name: dict.downloadSection.platforms.windows.name,
      icon: Monitor,
      description: dict.downloadSection.platforms.windows.version,
      downloads: [
        // {
        //   format: '.exe',
        //   url: base + version + '/Mind.Elixir_' + version + '_x64-setup.exe',
        // },
        {
          format: '.msi',
          url: base + version + '/Mind.Elixir_' + version + '_x64_en-US.msi',
        },
        {
          format: 'Microsoft Store',
          url: 'https://apps.microsoft.com/detail/9P9SMF4VVQ2V?hl=neutral&gl=US&ocid=pdpshare',
        },
      ],
    },
    {
      name: dict.downloadSection.platforms.macos.name,
      icon: Apple,
      description: dict.downloadSection.platforms.macos.version,
      downloads: [
        {
          format: '.dmg',
          url: base + version + '/Mind.Elixir_' + version + '_aarch64.dmg',
        },
      ],
    },
    {
      name: dict.downloadSection.platforms.linux.name,
      icon: Terminal,
      description: dict.downloadSection.platforms.linux.version,
      downloads: [
        {
          format: '.deb',
          url: base + version + '/Mind.Elixir_' + version + '_amd64.deb',
        },
        {
          format: '.rpm',
          url: base + version + '/Mind.Elixir-' + version + '-1.x86_64.rpm',
        },
      ],
    },
  ]

  return (
    <div className="my-26 max-w-[80vw] m-auto">
      <SectionTitle
        title={dict.downloadSection.sectionTitle}
        description={dict.downloadSection.sectionDescription}
        id="download"
      />

      {/* 版本信息 */}
      <div className="text-center mb-12">
        <div className="inline-flex items-center px-4 py-2 bg-purple-100 dark:bg-purple-900/30 rounded-full">
          <span className="text-sm font-medium text-purple-800 dark:text-purple-200">
            {dict.downloadSection.latestVersion} {version}
          </span>
        </div>
      </div>

      {/* 平台下载卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {platforms.map((platform) => {
          const IconComponent = platform.icon
          return (
            <div
              key={platform.name}
              className="rounded-lg border text-card-foreground bg-background border-none shadow-none"
              style={{
                opacity: 1,
                filter: 'blur(0px)',
                willChange: 'auto',
                transform: 'translateY(-6px)',
              }}
            >
              <div className="p-6 space-y-4">
                {/* 平台图标和信息 */}
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                    <IconComponent className="h-6 w-6" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold">{platform.name}</h3>
                    <p className="text-sm text-muted-foreground">
                      {platform.description}
                    </p>
                  </div>
                </div>

                {/* 下载选项 */}
                <div className="space-y-3">
                  {platform.downloads.map((download) => (
                    <a
                      download
                      target={
                        download.format === 'Microsoft Store'
                          ? '_blank'
                          : '_self'
                      }
                      key={download.format}
                      href={download.url}
                      className={`
                        w-full flex items-center justify-center px-4 py-3 rounded-lg transition-colors
                        ${'border border-input bg-background hover:bg-accent hover:text-accent-foreground'}
                      `}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      <span className="font-mono font-medium">
                        {download.format}
                      </span>
                    </a>
                  ))}
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* 其他版本和开源链接 */}
      <div className="mt-12 text-center space-y-4">
        <div className="flex justify-center gap-6">
          <a
            href="https://github.com/SSShooter/Mind-Elixir-Desktop-Release/releases"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center text-muted-foreground hover:text-primary text-sm transition-colors"
          >
            {dict.downloadSection.otherVersions || 'Other Versions'}
          </a>
          <span className="text-muted-foreground">•</span>
          <a
            href="https://github.com/SSShooter/mind-elixir-core"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center text-muted-foreground hover:text-primary text-sm transition-colors"
          >
            {dict.downloadSection.poweredByOpenSource}
          </a>
        </div>
      </div>
    </div>
  )
}
