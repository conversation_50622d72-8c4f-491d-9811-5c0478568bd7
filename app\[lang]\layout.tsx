import type { Locale } from "@/lib/i18n/config";
import { Metadata } from 'next';
import { getDictionary } from '@/lib/i18n/dictionaries';

interface LanguageLayoutProps {
  children: React.ReactNode;
  params: Promise<{ lang: Locale }>;
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ lang: Locale }>;
}): Promise<Metadata> {
  const { lang } = await params;
  const dict = await getDictionary(lang);

  return {
    title: dict.meta.title,
    description: dict.meta.description,
    keywords: dict.meta.keywords,
    openGraph: {
      title: dict.meta.openGraph.title,
      description: dict.meta.openGraph.description,
      siteName: dict.meta.openGraph.siteName,
      locale: lang,
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: dict.meta.twitter.title,
      description: dict.meta.twitter.description,
    },
    alternates: {
      languages: {
        'en': '/en',
        'zh': '/zh',
      },
    },
  };
}

export default async function LanguageLayout({
  children,
  params,
}: LanguageLayoutProps) {
  const { lang } = await params;

  return (
    <html
      lang={lang}
      suppressHydrationWarning
      className={` dark bg-white dark:bg-gray-950 text-black dark:text-white`}
    >
      <body>{children}</body>
    </html>
  );
}
