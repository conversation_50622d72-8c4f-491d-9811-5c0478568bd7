import Link from 'next/link';
import { BlogCard } from './blog-card';
import { Button } from '@/components/ui/button';
import type { BlogPostMeta } from '@/types/blog';
import type { Locale } from '@/lib/i18n/config';
import type { Dictionary } from '@/types/i18n';

interface BlogListProps {
  posts: BlogPostMeta[];
  lang: Locale;
  dict: Dictionary;
  selectedCategory?: string;
}

export function BlogList({ posts, lang, dict, selectedCategory = 'all' }: BlogListProps) {
  const categories = ['all', 'updates', 'tutorials', 'tips'] as const;

  const featuredPosts = selectedCategory === 'all' ? posts.filter(post => post.featured) : [];
  const regularPosts = posts.filter(post => !post.featured);

  return (
    <div className="space-y-8">
      {/* Category Filter */}
      <div className="flex flex-wrap gap-2">
        {categories.map((category) => (
          <Button
            key={category}
            variant={selectedCategory === category ? 'default' : 'outline'}
            size="sm"
            asChild
            className="capitalize"
          >
            <Link href={`/${lang}/blog/category/${category}`}>
              {dict.blog.categories[category]}
            </Link>
          </Button>
        ))}
      </div>

      {/* Featured Posts */}
      {selectedCategory === 'all' && featuredPosts.length > 0 && (
        <section>
          <h2 className="text-2xl font-bold mb-6">Featured Posts</h2>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {featuredPosts.map((post) => (
              <BlogCard
                key={`${post.slug}-${post.lang}`}
                post={post}
                lang={lang}
                dict={dict}
              />
            ))}
          </div>
        </section>
      )}

      {/* Regular Posts */}
      {regularPosts.length > 0 ? (
        <section>
          {selectedCategory === 'all' && featuredPosts.length > 0 && (
            <h2 className="text-2xl font-bold mb-6">Latest Posts</h2>
          )}
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {regularPosts.map((post) => (
              <BlogCard
                key={`${post.slug}-${post.lang}`}
                post={post}
                lang={lang}
                dict={dict}
              />
            ))}
          </div>
        </section>
      ) : (
        <div className="text-center py-12">
          <p className="text-muted-foreground text-lg">
            {dict.blog.noPostsFound}
          </p>
        </div>
      )}
    </div>
  );
}
