'use client'

import React, { forwardRef, useRef } from 'react'

import { cn } from '@/lib/utils'
import { AnimatedBeam } from '@/components/magicui/animated-beam'
import Image from 'next/image'
import logo from '@/assets/mind-elixir-desktop.svg'

const Circle = forwardRef<
  HTMLDivElement,
  { className?: string; children?: React.ReactNode }
>(({ className, children }, ref) => {
  return (
    <div
      ref={ref}
      className={cn(
        'z-10 flex items-center justify-center rounded-full border-2 bg-white p-3 shadow-[0_0_20px_-12px_rgba(0,0,0,0.8)]',
        className
      )}
    >
      {children}
    </div>
  )
})

Circle.displayName = 'Circle'

export function ExportTypes({ className }: { className?: string }) {
  const containerRef = useRef<HTMLDivElement>(null)
  const div1Ref = useRef<HTMLDivElement>(null)
  const div2Ref = useRef<HTMLDivElement>(null)
  const div3Ref = useRef<HTMLDivElement>(null)
  const div4Ref = useRef<HTMLDivElement>(null)
  const div5Ref = useRef<HTMLDivElement>(null)
  const div6Ref = useRef<HTMLDivElement>(null)
  const div7Ref = useRef<HTMLDivElement>(null)

  return (
    <div
      className={cn(
        'relative flex h-[300px] w-full items-center justify-center overflow-hidden p-3',
        className
      )}
      ref={containerRef}
    >
      <div className="flex size-full max-w-lg flex-row items-stretch justify-between gap-10">
        <div className="flex flex-col justify-center">
          <Circle ref={div7Ref}>
            <Icons.user />
          </Circle>
        </div>
        <div className="flex flex-col justify-center">
          <Circle ref={div6Ref} className="size-16">
            <Image src={logo} width={24} height={24} alt="MindElixir" />
          </Circle>
        </div>
        <div className="flex flex-col justify-center gap-2">
          <Circle ref={div1Ref}>
            <Icons.jpeg />
          </Circle>
          <Circle ref={div2Ref}>
            <Icons.webp />
          </Circle>
          <Circle ref={div3Ref}>
            <Icons.png />
          </Circle>
          <Circle ref={div4Ref}>
            <Icons.html />
          </Circle>
          <Circle ref={div5Ref}>
            <Icons.json />
          </Circle>
        </div>
      </div>

      {/* AnimatedBeams */}
      <AnimatedBeam
        containerRef={containerRef}
        fromRef={div1Ref}
        toRef={div6Ref}
        duration={3}
      />
      <AnimatedBeam
        containerRef={containerRef}
        fromRef={div2Ref}
        toRef={div6Ref}
        duration={3}
      />
      <AnimatedBeam
        containerRef={containerRef}
        fromRef={div3Ref}
        toRef={div6Ref}
        duration={3}
      />
      <AnimatedBeam
        containerRef={containerRef}
        fromRef={div4Ref}
        toRef={div6Ref}
        duration={3}
      />
      <AnimatedBeam
        containerRef={containerRef}
        fromRef={div5Ref}
        toRef={div6Ref}
        duration={3}
      />
      <AnimatedBeam
        containerRef={containerRef}
        fromRef={div6Ref}
        toRef={div7Ref}
        duration={3}
      />
    </div>
  )
}

const Icons = {
  html: () => (
    <svg
      width="40"
      height="40"
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9 0.75H26.0439L34.25 8.28711V36C34.25 37.7949 32.7948 39.2499 31 39.25H9C7.20507 39.25 5.75 37.7949 5.75 36V4C5.75 2.20508 7.20508 0.75 9 0.75Z"
        stroke="#D0D5DD"
        strokeWidth="1.5"
      />
      <mask id="path-2-inside-1_2001_3559" fill="white">
        <path d="M25 1H33.5V9.5H26C25.4477 9.5 25 9.05229 25 8.5V1Z" />
      </mask>
      <path
        d="M25 1H33.5H25ZM33.5 10.5H26C24.8954 10.5 24 9.60457 24 8.5H26H33.5V10.5ZM26 10.5C24.8954 10.5 24 9.60457 24 8.5V1H26V8.5V10.5ZM33.5 1V9.5V1Z"
        fill="#D0D5DD"
        mask="url(#path-2-inside-1_2001_3559)"
      />
      <rect x="1" y="20" width="32" height="13" rx="2" fill="#E63737" />
      <path
        d="M10.116 22.98V30H8.40602V27.11H5.74602V30H4.03602V22.98H5.74602V25.73H8.40602V22.98H10.116ZM16.4005 22.98V24.35H14.5405V30H12.8305V24.35H10.9705V22.98H16.4005ZM25.2087 22.98V30H23.4987V25.79L21.9287 30H20.5487L18.9687 25.78V30H17.2587V22.98H19.2787L21.2487 27.84L23.1987 22.98H25.2087ZM28.1484 28.68H30.3884V30H26.4384V22.98H28.1484V28.68Z"
        fill="white"
      />
    </svg>
  ),
  jpeg: () => (
    <svg
      width="40"
      height="41"
      viewBox="0 0 40 41"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_2001_2827)">
        <path
          d="M34.9998 8.6597V36.7025C34.9998 38.9117 33.209 40.7025 30.9998 40.7025H9C6.79086 40.7025 5 38.9117 5 36.7025V4.70252C5 2.49338 6.79086 0.702515 9 0.702515H26.3367L34.9998 8.6597Z"
          fill="#E65A4D"
        />
        <g filter="url(#filter0_d_2001_2827)">
          <path
            d="M34.9998 8.6597H27.3367C26.7844 8.6597 26.3367 8.21199 26.3367 7.6597V0.702515L34.9998 8.6597Z"
            fill="#F8AEA7"
          />
        </g>
        <path
          d="M13.246 24.3845V28.6865C13.246 29.3525 13.057 29.8655 12.679 30.2255C12.307 30.5855 11.803 30.7655 11.167 30.7655C10.501 30.7655 9.96703 30.5765 9.56503 30.1985C9.16303 29.8205 8.96203 29.2835 8.96203 28.5875H10.492C10.492 28.8515 10.546 29.0525 10.654 29.1905C10.762 29.3225 10.918 29.3885 11.122 29.3885C11.308 29.3885 11.452 29.3285 11.554 29.2085C11.656 29.0885 11.707 28.9145 11.707 28.6865V24.3845H13.246ZM19.3042 26.4185C19.3042 26.7845 19.2202 27.1205 19.0522 27.4265C18.8842 27.7265 18.6262 27.9695 18.2782 28.1555C17.9302 28.3415 17.4982 28.4345 16.9822 28.4345H16.0282V30.7025H14.4892V24.3845H16.9822C17.4862 24.3845 17.9122 24.4715 18.2602 24.6455C18.6082 24.8195 18.8692 25.0595 19.0432 25.3655C19.2172 25.6715 19.3042 26.0225 19.3042 26.4185ZM16.8652 27.2105C17.1592 27.2105 17.3782 27.1415 17.5222 27.0035C17.6662 26.8655 17.7382 26.6705 17.7382 26.4185C17.7382 26.1665 17.6662 25.9715 17.5222 25.8335C17.3782 25.6955 17.1592 25.6265 16.8652 25.6265H16.0282V27.2105H16.8652ZM21.6444 25.6175V26.8955H23.7054V28.0835H21.6444V29.4695H23.9754V30.7025H20.1054V24.3845H23.9754V25.6175H21.6444ZM29.1685 26.3825C29.0545 26.1725 28.8895 26.0135 28.6735 25.9055C28.4635 25.7915 28.2145 25.7345 27.9265 25.7345C27.4285 25.7345 27.0295 25.8995 26.7295 26.2295C26.4295 26.5535 26.2795 26.9885 26.2795 27.5345C26.2795 28.1165 26.4355 28.5725 26.7475 28.9025C27.0655 29.2265 27.5005 29.3885 28.0525 29.3885C28.4305 29.3885 28.7485 29.2925 29.0065 29.1005C29.2705 28.9085 29.4625 28.6325 29.5825 28.2725H27.6295V27.1385H30.9775V28.5695C30.8635 28.9535 30.6685 29.3105 30.3925 29.6405C30.1225 29.9705 29.7775 30.2375 29.3575 30.4415C28.9375 30.6455 28.4635 30.7475 27.9355 30.7475C27.3115 30.7475 26.7535 30.6125 26.2615 30.3425C25.7755 30.0665 25.3945 29.6855 25.1185 29.1995C24.8485 28.7135 24.7135 28.1585 24.7135 27.5345C24.7135 26.9105 24.8485 26.3555 25.1185 25.8695C25.3945 25.3775 25.7755 24.9965 26.2615 24.7265C26.7475 24.4505 27.3025 24.3125 27.9265 24.3125C28.6825 24.3125 29.3185 24.4955 29.8345 24.8615C30.3565 25.2275 30.7015 25.7345 30.8695 26.3825H29.1685Z"
          fill="white"
        />
      </g>
      <defs>
        <filter
          id="filter0_d_2001_2827"
          x="23.3367"
          y="-0.297485"
          width="12.6631"
          height="11.9572"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dx="-1" dy="1" />
          <feGaussianBlur stdDeviation="1" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_2001_2827"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_2001_2827"
            result="shape"
          />
        </filter>
        <clipPath id="clip0_2001_2827">
          <rect
            width="40"
            height="40"
            fill="white"
            transform="translate(0 0.702515)"
          />
        </clipPath>
      </defs>
    </svg>
  ),
  json: () => (
    <svg
      width="40"
      height="40"
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9 0.75H26.0439L34.25 8.28711V36C34.25 37.7949 32.7948 39.2499 31 39.25H9C7.20507 39.25 5.75 37.7949 5.75 36V4C5.75 2.20508 7.20508 0.75 9 0.75Z"
        stroke="#D0D5DD"
        strokeWidth="1.5"
      />
      <mask id="path-2-inside-1_2001_3565" fill="white">
        <path d="M25 1H33.5V9.5H26C25.4477 9.5 25 9.05229 25 8.5V1Z" />
      </mask>
      <path
        d="M25 1H33.5H25ZM33.5 10.5H26C24.8954 10.5 24 9.60457 24 8.5H26H33.5V10.5ZM26 10.5C24.8954 10.5 24 9.60457 24 8.5V1H26V8.5V10.5ZM33.5 1V9.5V1Z"
        fill="#D0D5DD"
        mask="url(#path-2-inside-1_2001_3565)"
      />
      <rect x="1" y="20" width="32" height="13" rx="2" fill="#434563" />
      <path
        d="M8.36277 22.98V27.76C8.36277 28.5 8.15277 29.07 7.73277 29.47C7.31944 29.87 6.75944 30.07 6.05277 30.07C5.31277 30.07 4.71944 29.86 4.27277 29.44C3.82611 29.02 3.60277 28.4233 3.60277 27.65H5.30277C5.30277 27.9433 5.36277 28.1667 5.48277 28.32C5.60277 28.4667 5.77611 28.54 6.00277 28.54C6.20944 28.54 6.36944 28.4733 6.48277 28.34C6.59611 28.2067 6.65277 28.0133 6.65277 27.76V22.98H8.36277ZM12.284 30.07C11.7707 30.07 11.3107 29.9867 10.904 29.82C10.4974 29.6533 10.1707 29.4067 9.92402 29.08C9.68402 28.7533 9.55736 28.36 9.54402 27.9H11.364C11.3907 28.16 11.4807 28.36 11.634 28.5C11.7874 28.6333 11.9874 28.7 12.234 28.7C12.4874 28.7 12.6874 28.6433 12.834 28.53C12.9807 28.41 13.054 28.2467 13.054 28.04C13.054 27.8667 12.994 27.7233 12.874 27.61C12.7607 27.4967 12.6174 27.4033 12.444 27.33C12.2774 27.2567 12.0374 27.1733 11.724 27.08C11.2707 26.94 10.9007 26.8 10.614 26.66C10.3274 26.52 10.0807 26.3133 9.87402 26.04C9.66736 25.7667 9.56402 25.41 9.56402 24.97C9.56402 24.3167 9.80069 23.8067 10.274 23.44C10.7474 23.0667 11.364 22.88 12.124 22.88C12.8974 22.88 13.5207 23.0667 13.994 23.44C14.4674 23.8067 14.7207 24.32 14.754 24.98H12.904C12.8907 24.7533 12.8074 24.5767 12.654 24.45C12.5007 24.3167 12.304 24.25 12.064 24.25C11.8574 24.25 11.6907 24.3067 11.564 24.42C11.4374 24.5267 11.374 24.6833 11.374 24.89C11.374 25.1167 11.4807 25.2933 11.694 25.42C11.9074 25.5467 12.2407 25.6833 12.694 25.83C13.1474 25.9833 13.514 26.13 13.794 26.27C14.0807 26.41 14.3274 26.6133 14.534 26.88C14.7407 27.1467 14.844 27.49 14.844 27.91C14.844 28.31 14.7407 28.6733 14.534 29C14.334 29.3267 14.0407 29.5867 13.654 29.78C13.2674 29.9733 12.8107 30.07 12.284 30.07ZM19.2164 30.07C18.5564 30.07 17.9497 29.9167 17.3964 29.61C16.8497 29.3033 16.413 28.8767 16.0864 28.33C15.7664 27.7767 15.6064 27.1567 15.6064 26.47C15.6064 25.7833 15.7664 25.1667 16.0864 24.62C16.413 24.0733 16.8497 23.6467 17.3964 23.34C17.9497 23.0333 18.5564 22.88 19.2164 22.88C19.8764 22.88 20.4797 23.0333 21.0264 23.34C21.5797 23.6467 22.013 24.0733 22.3264 24.62C22.6464 25.1667 22.8064 25.7833 22.8064 26.47C22.8064 27.1567 22.6464 27.7767 22.3264 28.33C22.0064 28.8767 21.573 29.3033 21.0264 29.61C20.4797 29.9167 19.8764 30.07 19.2164 30.07ZM19.2164 28.51C19.7764 28.51 20.223 28.3233 20.5564 27.95C20.8964 27.5767 21.0664 27.0833 21.0664 26.47C21.0664 25.85 20.8964 25.3567 20.5564 24.99C20.223 24.6167 19.7764 24.43 19.2164 24.43C18.6497 24.43 18.1964 24.6133 17.8564 24.98C17.523 25.3467 17.3564 25.8433 17.3564 26.47C17.3564 27.09 17.523 27.5867 17.8564 27.96C18.1964 28.3267 18.6497 28.51 19.2164 28.51ZM30.0377 30H28.3277L25.4677 25.67V30H23.7577V22.98H25.4677L28.3277 27.33V22.98H30.0377V30Z"
        fill="white"
      />
    </svg>
  ),
  png: () => (
    <svg
      width="40"
      height="41"
      viewBox="0 0 40 41"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_2001_2893)">
        <path
          d="M34.9998 8.6597V36.7025C34.9998 38.9117 33.209 40.7025 30.9998 40.7025H9C6.79086 40.7025 5 38.9117 5 36.7025V4.70252C5 2.49338 6.79086 0.702515 9 0.702515H26.3367L34.9998 8.6597Z"
          fill="#6172F3"
        />
        <g filter="url(#filter0_d_2001_2893)">
          <path
            d="M34.9998 8.6597H27.3367C26.7844 8.6597 26.3367 8.21199 26.3367 7.6597V0.702515L34.9998 8.6597Z"
            fill="#C7D7FE"
          />
        </g>
        <path
          d="M15.7534 26.4185C15.7534 26.7845 15.6694 27.1205 15.5014 27.4265C15.3334 27.7265 15.0754 27.9695 14.7274 28.1555C14.3794 28.3415 13.9474 28.4345 13.4314 28.4345H12.4774V30.7025H10.9384V24.3845H13.4314C13.9354 24.3845 14.3614 24.4715 14.7094 24.6455C15.0574 24.8195 15.3184 25.0595 15.4924 25.3655C15.6664 25.6715 15.7534 26.0225 15.7534 26.4185ZM13.3144 27.2105C13.6084 27.2105 13.8274 27.1415 13.9714 27.0035C14.1154 26.8655 14.1874 26.6705 14.1874 26.4185C14.1874 26.1665 14.1154 25.9715 13.9714 25.8335C13.8274 25.6955 13.6084 25.6265 13.3144 25.6265H12.4774V27.2105H13.3144ZM22.2066 30.7025H20.6676L18.0936 26.8055V30.7025H16.5546V24.3845H18.0936L20.6676 28.2995V24.3845H22.2066V30.7025ZM27.5162 26.3825C27.4022 26.1725 27.2372 26.0135 27.0212 25.9055C26.8112 25.7915 26.5622 25.7345 26.2742 25.7345C25.7762 25.7345 25.3772 25.8995 25.0772 26.2295C24.7772 26.5535 24.6272 26.9885 24.6272 27.5345C24.6272 28.1165 24.7832 28.5725 25.0952 28.9025C25.4132 29.2265 25.8482 29.3885 26.4002 29.3885C26.7782 29.3885 27.0962 29.2925 27.3542 29.1005C27.6182 28.9085 27.8102 28.6325 27.9302 28.2725H25.9772V27.1385H29.3252V28.5695C29.2112 28.9535 29.0162 29.3105 28.7402 29.6405C28.4702 29.9705 28.1252 30.2375 27.7052 30.4415C27.2852 30.6455 26.8112 30.7475 26.2832 30.7475C25.6592 30.7475 25.1012 30.6125 24.6092 30.3425C24.1232 30.0665 23.7422 29.6855 23.4662 29.1995C23.1962 28.7135 23.0612 28.1585 23.0612 27.5345C23.0612 26.9105 23.1962 26.3555 23.4662 25.8695C23.7422 25.3775 24.1232 24.9965 24.6092 24.7265C25.0952 24.4505 25.6502 24.3125 26.2742 24.3125C27.0302 24.3125 27.6662 24.4955 28.1822 24.8615C28.7042 25.2275 29.0492 25.7345 29.2172 26.3825H27.5162Z"
          fill="white"
        />
      </g>
      <defs>
        <filter
          id="filter0_d_2001_2893"
          x="23.3367"
          y="-0.297485"
          width="12.6631"
          height="11.9572"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dx="-1" dy="1" />
          <feGaussianBlur stdDeviation="1" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_2001_2893"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_2001_2893"
            result="shape"
          />
        </filter>
        <clipPath id="clip0_2001_2893">
          <rect
            width="40"
            height="40"
            fill="white"
            transform="translate(0 0.702515)"
          />
        </clipPath>
      </defs>
    </svg>
  ),
  webp: () => (
    <svg
      width="40"
      height="41"
      viewBox="0 0 40 41"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_2001_2905)">
        <path
          d="M34.9998 8.6597V36.7025C34.9998 38.9117 33.209 40.7025 30.9998 40.7025H9C6.79086 40.7025 5 38.9117 5 36.7025V4.70252C5 2.49338 6.79086 0.702515 9 0.702515H26.3367L34.9998 8.6597Z"
          fill="#458C47"
        />
        <g filter="url(#filter0_d_2001_2905)">
          <path
            d="M34.9998 8.6597H27.3367C26.7844 8.6597 26.3367 8.21199 26.3367 7.6597V0.702515L34.9998 8.6597Z"
            fill="#AEE8B0"
          />
        </g>
        <path
          d="M16.3101 24.3845L14.6631 30.7025H12.8001L11.7921 26.5445L10.7481 30.7025H8.88511L7.28311 24.3845H8.93011L9.83911 28.9835L10.9641 24.3845H12.6561L13.7361 28.9835L14.6541 24.3845H16.3101ZM18.6209 25.6175V26.8955H20.6819V28.0835H18.6209V29.4695H20.9519V30.7025H17.0819V24.3845H20.9519V25.6175H18.6209ZM25.7761 27.4625C26.1421 27.5405 26.4361 27.7235 26.6581 28.0115C26.8801 28.2935 26.9911 28.6175 26.9911 28.9835C26.9911 29.5115 26.8051 29.9315 26.4331 30.2435C26.0671 30.5495 25.5541 30.7025 24.8941 30.7025H21.9511V24.3845H24.7951C25.4371 24.3845 25.9381 24.5315 26.2981 24.8255C26.6641 25.1195 26.8471 25.5185 26.8471 26.0225C26.8471 26.3945 26.7481 26.7035 26.5501 26.9495C26.3581 27.1955 26.1001 27.3665 25.7761 27.4625ZM23.4901 26.9405H24.4981C24.7501 26.9405 24.9421 26.8865 25.0741 26.7785C25.2121 26.6645 25.2811 26.4995 25.2811 26.2835C25.2811 26.0675 25.2121 25.9025 25.0741 25.7885C24.9421 25.6745 24.7501 25.6175 24.4981 25.6175H23.4901V26.9405ZM24.6241 29.4605C24.8821 29.4605 25.0801 29.4035 25.2181 29.2895C25.3621 29.1695 25.4341 28.9985 25.4341 28.7765C25.4341 28.5545 25.3591 28.3805 25.2091 28.2545C25.0651 28.1285 24.8641 28.0655 24.6061 28.0655H23.4901V29.4605H24.6241ZM32.6987 26.4185C32.6987 26.7845 32.6147 27.1205 32.4467 27.4265C32.2787 27.7265 32.0207 27.9695 31.6727 28.1555C31.3247 28.3415 30.8927 28.4345 30.3767 28.4345H29.4227V30.7025H27.8837V24.3845H30.3767C30.8807 24.3845 31.3067 24.4715 31.6547 24.6455C32.0027 24.8195 32.2637 25.0595 32.4377 25.3655C32.6117 25.6715 32.6987 26.0225 32.6987 26.4185ZM30.2597 27.2105C30.5537 27.2105 30.7727 27.1415 30.9167 27.0035C31.0607 26.8655 31.1327 26.6705 31.1327 26.4185C31.1327 26.1665 31.0607 25.9715 30.9167 25.8335C30.7727 25.6955 30.5537 25.6265 30.2597 25.6265H29.4227V27.2105H30.2597Z"
          fill="white"
        />
      </g>
      <defs>
        <filter
          id="filter0_d_2001_2905"
          x="23.3367"
          y="-0.297485"
          width="12.6631"
          height="11.9572"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dx="-1" dy="1" />
          <feGaussianBlur stdDeviation="1" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_2001_2905"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_2001_2905"
            result="shape"
          />
        </filter>
        <clipPath id="clip0_2001_2905">
          <rect
            width="40"
            height="40"
            fill="white"
            transform="translate(0 0.702515)"
          />
        </clipPath>
      </defs>
    </svg>
  ),
  zapier: () => (
    <svg
      width="105"
      height="28"
      viewBox="0 0 244 66"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M57.1877 45.2253L57.1534 45.1166L78.809 25.2914V15.7391H44.0663V25.2914H64.8181L64.8524 25.3829L43.4084 45.2253V54.7775H79.1579V45.2253H57.1877Z"
        fill="#201515"
      />
      <path
        d="M100.487 14.8297C96.4797 14.8297 93.2136 15.434 90.6892 16.6429C88.3376 17.6963 86.3568 19.4321 85.0036 21.6249C83.7091 23.8321 82.8962 26.2883 82.6184 28.832L93.1602 30.3135C93.5415 28.0674 94.3042 26.4754 95.4482 25.5373C96.7486 24.5562 98.3511 24.0605 99.9783 24.136C102.118 24.136 103.67 24.7079 104.634 25.8519C105.59 26.9959 106.076 28.5803 106.076 30.6681V31.7091H95.9401C90.7807 31.7091 87.0742 32.8531 84.8206 35.1411C82.5669 37.429 81.442 40.4492 81.4458 44.2014C81.4458 48.0452 82.5707 50.9052 84.8206 52.7813C87.0704 54.6574 89.8999 55.5897 93.3089 55.5783C97.5379 55.5783 100.791 54.1235 103.067 51.214C104.412 49.426 105.372 47.3793 105.887 45.2024H106.27L107.723 54.7546H117.275V30.5651C117.275 25.5659 115.958 21.6936 113.323 18.948C110.688 16.2024 106.409 14.8297 100.487 14.8297ZM103.828 44.6475C102.312 45.9116 100.327 46.5408 97.8562 46.5408C95.8199 46.5408 94.4052 46.1843 93.6121 45.4712C93.2256 45.1338 92.9182 44.7155 92.7116 44.246C92.505 43.7764 92.4043 43.2671 92.4166 42.7543C92.3941 42.2706 92.4702 41.7874 92.6403 41.3341C92.8104 40.8808 93.071 40.4668 93.4062 40.1174C93.7687 39.7774 94.1964 39.5145 94.6633 39.3444C95.1303 39.1743 95.6269 39.1006 96.1231 39.1278H106.093V39.7856C106.113 40.7154 105.919 41.6374 105.527 42.4804C105.134 43.3234 104.553 44.0649 103.828 44.6475Z"
        fill="#201515"
      />
      <path
        d="M175.035 15.7391H163.75V54.7833H175.035V15.7391Z"
        fill="#201515"
      />
      <path
        d="M241.666 15.7391C238.478 15.7391 235.965 16.864 234.127 19.1139C232.808 20.7307 231.805 23.1197 231.119 26.2809H230.787L229.311 15.7391H219.673V54.7775H230.959V34.7578C230.959 32.2335 231.55 30.2982 232.732 28.9521C233.914 27.606 236.095 26.933 239.275 26.933H243.559V15.7391H241.666Z"
        fill="#201515"
      />
      <path
        d="M208.473 17.0147C205.839 15.4474 202.515 14.6657 198.504 14.6695C192.189 14.6695 187.247 16.4675 183.678 20.0634C180.108 23.6593 178.324 28.6166 178.324 34.9352C178.233 38.7553 179.067 42.5407 180.755 45.9689C182.3 49.0238 184.706 51.5592 187.676 53.2618C190.665 54.9892 194.221 55.8548 198.344 55.8586C201.909 55.8586 204.887 55.3095 207.278 54.2113C209.526 53.225 211.483 51.6791 212.964 49.7211C214.373 47.7991 215.42 45.6359 216.052 43.3377L206.329 40.615C205.919 42.1094 205.131 43.4728 204.041 44.5732C202.942 45.6714 201.102 46.2206 198.521 46.2206C195.451 46.2206 193.163 45.3416 191.657 43.5837C190.564 42.3139 189.878 40.5006 189.575 38.1498H216.201C216.31 37.0515 216.367 36.1306 216.367 35.387V32.9561C216.431 29.6903 215.757 26.4522 214.394 23.4839C213.118 20.7799 211.054 18.5248 208.473 17.0147ZM198.178 23.9758C202.754 23.9758 205.348 26.2275 205.962 30.731H189.775C190.032 29.2284 190.655 27.8121 191.588 26.607C193.072 24.8491 195.268 23.972 198.178 23.9758Z"
        fill="#201515"
      />
      <path
        d="M169.515 0.00366253C168.666 -0.0252113 167.82 0.116874 167.027 0.421484C166.234 0.726094 165.511 1.187 164.899 1.77682C164.297 2.3723 163.824 3.08658 163.512 3.87431C163.2 4.66204 163.055 5.50601 163.086 6.35275C163.056 7.20497 163.201 8.05433 163.514 8.84781C163.826 9.64129 164.299 10.3619 164.902 10.9646C165.505 11.5673 166.226 12.0392 167.02 12.3509C167.814 12.6626 168.663 12.8074 169.515 12.7762C170.362 12.8082 171.206 12.6635 171.994 12.3514C172.782 12.0392 173.496 11.5664 174.091 10.963C174.682 10.3534 175.142 9.63077 175.446 8.83849C175.75 8.04621 175.89 7.20067 175.859 6.35275C175.898 5.50985 175.761 4.66806 175.456 3.88115C175.151 3.09424 174.686 2.37951 174.09 1.78258C173.493 1.18565 172.779 0.719644 171.992 0.414327C171.206 0.109011 170.364 -0.0288946 169.521 0.00938803L169.515 0.00366253Z"
        fill="#201515"
      />
      <path
        d="M146.201 14.6695C142.357 14.6695 139.268 15.8764 136.935 18.2902C135.207 20.0786 133.939 22.7479 133.131 26.2981H132.771L131.295 15.7563H121.657V66H132.942V45.3054H133.354C133.698 46.6852 134.181 48.0267 134.795 49.3093C135.75 51.3986 137.316 53.1496 139.286 54.3314C141.328 55.446 143.629 56.0005 145.955 55.9387C150.68 55.9387 154.277 54.0988 156.748 50.419C159.219 46.7392 160.455 41.6046 160.455 35.0153C160.455 28.6509 159.259 23.6689 156.869 20.0691C154.478 16.4694 150.922 14.6695 146.201 14.6695ZM147.345 42.9602C146.029 44.8668 143.97 45.8201 141.167 45.8201C140.012 45.8735 138.86 45.6507 137.808 45.1703C136.755 44.6898 135.832 43.9656 135.116 43.0574C133.655 41.2233 132.927 38.7122 132.931 35.5243V34.7807C132.931 31.5432 133.659 29.0646 135.116 27.3448C136.572 25.625 138.59 24.7747 141.167 24.7937C144.02 24.7937 146.092 25.6994 147.385 27.5107C148.678 29.322 149.324 31.8483 149.324 35.0896C149.332 38.4414 148.676 41.065 147.356 42.9602H147.345Z"
        fill="#201515"
      />
      <path d="M39.0441 45.2253H0V54.789H39.0441V45.2253Z" fill="#FF4F00" />
    </svg>
  ),
  messenger: () => (
    <svg
      width="100"
      height="100"
      viewBox="0 0 48 48"
      xmlns="http://www.w3.org/2000/svg"
    >
      <radialGradient
        id="8O3wK6b5ASW2Wn6hRCB5xa_YFbzdUk7Q3F8_gr1"
        cx="11.087"
        cy="7.022"
        r="47.612"
        gradientTransform="matrix(1 0 0 -1 0 50)"
        gradientUnits="userSpaceOnUse"
      >
        <stop offset="0" stopColor="#1292ff"></stop>
        <stop offset=".079" stopColor="#2982ff"></stop>
        <stop offset=".23" stopColor="#4e69ff"></stop>
        <stop offset=".351" stopColor="#6559ff"></stop>
        <stop offset=".428" stopColor="#6d53ff"></stop>
        <stop offset=".754" stopColor="#df47aa"></stop>
        <stop offset=".946" stopColor="#ff6257"></stop>
      </radialGradient>
      <path
        fill="url(#8O3wK6b5ASW2Wn6hRCB5xa_YFbzdUk7Q3F8_gr1)"
        d="M44,23.5C44,34.27,35.05,43,24,43c-1.651,0-3.25-0.194-4.784-0.564	c-0.465-0.112-0.951-0.069-1.379,0.145L13.46,44.77C12.33,45.335,11,44.513,11,43.249v-4.025c0-0.575-0.257-1.111-0.681-1.499	C6.425,34.165,4,29.11,4,23.5C4,12.73,12.95,4,24,4S44,12.73,44,23.5z"
      />
      <path
        d="M34.992,17.292c-0.428,0-0.843,0.142-1.2,0.411l-5.694,4.215	c-0.133,0.1-0.28,0.15-0.435,0.15c-0.15,0-0.291-0.047-0.41-0.136l-3.972-2.99c-0.808-0.601-1.76-0.918-2.757-0.918	c-1.576,0-3.025,0.791-3.876,2.116l-1.211,1.891l-4.12,6.695c-0.392,0.614-0.422,1.372-0.071,2.014	c0.358,0.654,1.034,1.06,1.764,1.06c0.428,0,0.843-0.142,1.2-0.411l5.694-4.215c0.133-0.1,0.28-0.15,0.435-0.15	c0.15,0,0.291,0.047,0.41,0.136l3.972,2.99c0.809,0.602,1.76,0.918,2.757,0.918c1.576,0,3.025-0.791,3.876-2.116l1.211-1.891	l4.12-6.695c0.392-0.614,0.422-1.372,0.071-2.014C36.398,17.698,35.722,17.292,34.992,17.292L34.992,17.292z"
        opacity=".05"
      />
      <path
        d="M34.992,17.792c-0.319,0-0.63,0.107-0.899,0.31l-5.697,4.218	c-0.216,0.163-0.468,0.248-0.732,0.248c-0.259,0-0.504-0.082-0.71-0.236l-3.973-2.991c-0.719-0.535-1.568-0.817-2.457-0.817	c-1.405,0-2.696,0.705-3.455,1.887l-1.21,1.891l-4.115,6.688c-0.297,0.465-0.32,1.033-0.058,1.511c0.266,0.486,0.787,0.8,1.325,0.8	c0.319,0,0.63-0.107,0.899-0.31l5.697-4.218c0.216-0.163,0.468-0.248,0.732-0.248c0.259,0,0.504,0.082,0.71,0.236l3.973,2.991	c0.719,0.535,1.568,0.817,2.457,0.817c1.405,0,2.696-0.705,3.455-1.887l1.21-1.891l4.115-6.688c0.297-0.465,0.32-1.033,0.058-1.511	C36.051,18.106,35.531,17.792,34.992,17.792L34.992,17.792z"
        opacity=".07"
      />
      <path
        fill="#ffffff"
        d="M34.394,18.501l-5.7,4.22c-0.61,0.46-1.44,0.46-2.04,0.01L22.68,19.74	c-1.68-1.25-4.06-0.82-5.19,0.94l-1.21,1.89l-4.11,6.68c-0.6,0.94,0.55,2.01,1.44,1.34l5.7-4.22c0.61-0.46,1.44-0.46,2.04-0.01	l3.974,2.991c1.68,1.25,4.06,0.82,5.19-0.94l1.21-1.89l4.11-6.68C36.434,18.901,35.284,17.831,34.394,18.501z"
      />
    </svg>
  ),
  user: () => (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="#000000"
      strokeWidth="2"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2" />
      <circle cx="12" cy="7" r="4" />
    </svg>
  ),
}
