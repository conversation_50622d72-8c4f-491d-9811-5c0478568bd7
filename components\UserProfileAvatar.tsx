'use client'
import React from 'react'
import { cn } from '@/lib/utils'
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar'

type UserType = 'annual' | 'lifetime' | undefined

interface UserProfileAvatarProps {
  image?: string
  name?: string
  type?: UserType
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

export default function UserProfileAvatar({
  image,
  name,
  type,
  size = 'md',
  className,
}: UserProfileAvatarProps) {
  const isLifetime = type === 'lifetime'
  const isAnnual = type === 'annual'
  const hasGlow = isLifetime || isAnnual

  // Size mappings
  const sizeClasses = {
    sm: 'size-8',
    md: 'size-12',
    lg: 'size-16',
  }

  // Get initials for fallback
  const getInitials = () => {
    if (!name) return 'U'
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2)
  }

  return (
    <div className={cn(`relative ${hasGlow ? 'p-1' : ''}`, className)}>
      <Avatar
        className={cn(
          sizeClasses[size],
          hasGlow ? 'relative z-10' : ''
        )}
      >
        <AvatarImage src={image} alt={name || 'User'} />
        <AvatarFallback>
          {getInitials()}
        </AvatarFallback>
      </Avatar>

      {isLifetime && (
        <div
          className="absolute inset-0 rounded-full animate-pulse"
          style={{
            background: 'linear-gradient(45deg, #6F49E0, #308CFE, #FE0D88, #6F49E0)',
            backgroundSize: '200% 200%',
            animation: 'gradient-animation 5s ease infinite, pulse 3s infinite',
            boxShadow: '0 0 10px 2px rgba(111, 73, 224, 0.6), 0 0 20px 4px rgba(254, 13, 136, 0.4)',
            filter: 'blur(4px)',
            opacity: 0.8,
            zIndex: 0
          }}
        />
      )}
      {isAnnual && (
        <div
          className="absolute inset-0 rounded-full animate-pulse"
          style={{
            background: 'linear-gradient(45deg, #FFD700, #FFA500, #F4BB44, #FFD700)',
            backgroundSize: '200% 200%',
            animation: 'gradient-animation 5s ease infinite, pulse 3s infinite',
            boxShadow: '0 0 10px 2px rgba(255, 215, 0, 0.6), 0 0 20px 4px rgba(255, 165, 0, 0.4)',
            filter: 'blur(4px)',
            opacity: 0.7,
            zIndex: 0
          }}
        />
      )}
    </div>
  )
}
