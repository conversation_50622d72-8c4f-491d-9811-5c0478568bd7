import 'server-only';
import fs from 'fs';
import path from 'path';
import matter from 'gray-matter';
import { remark } from 'remark';
import html from 'remark-html';
import type { BlogPost, BlogPostMeta, BlogFrontMatter } from '@/types/blog';
import type { Locale } from '@/lib/i18n/config';

const postsDirectory = path.join(process.cwd(), 'content/blog');

// Ensure the blog directory exists
if (!fs.existsSync(postsDirectory)) {
  fs.mkdirSync(postsDirectory, { recursive: true });
}

export async function getAllPosts(lang: Locale): Promise<BlogPostMeta[]> {
  try {
    const fileNames = fs.readdirSync(postsDirectory);
    const allPostsData = fileNames
      .filter((fileName) => fileName.endsWith('.md'))
      .map((fileName) => {
        const slug = fileName.replace(/\.md$/, '');
        const fullPath = path.join(postsDirectory, fileName);
        const fileContents = fs.readFileSync(fullPath, 'utf8');
        const matterResult = matter(fileContents);
        const frontMatter = matterResult.data as BlogFrontMatter;

        // Extract language from filename (e.g., post-title.en.md or post-title.zh.md)
        const langMatch = slug.match(/\.([a-z]{2})$/);
        const postLang = langMatch ? langMatch[1] as Locale : 'en';
        const cleanSlug = slug.replace(/\.[a-z]{2}$/, '');

        return {
          slug: cleanSlug,
          lang: postLang,
          title: frontMatter.title,
          description: frontMatter.description,
          date: frontMatter.date,
          category: frontMatter.category,
          readTime: frontMatter.readTime || calculateReadTime(matterResult.content),
          author: frontMatter.author,
          tags: frontMatter.tags,
          featured: frontMatter.featured,
        } as BlogPostMeta;
      })
      .filter((post) => post.lang === lang)
      .sort((a, b) => (new Date(a.date) < new Date(b.date) ? 1 : -1));

    return allPostsData;
  } catch (error) {
    console.error('Error reading blog posts:', error);
    return [];
  }
}

export async function getPostBySlug(slug: string, lang: Locale): Promise<BlogPost | null> {
  try {
    const fileName = `${slug}.${lang}.md`;
    const fullPath = path.join(postsDirectory, fileName);

    if (!fs.existsSync(fullPath)) {
      return null;
    }

    const fileContents = fs.readFileSync(fullPath, 'utf8');
    const matterResult = matter(fileContents);
    const frontMatter = matterResult.data as BlogFrontMatter;

    // Convert markdown to HTML
    const processedContent = await remark()
      .use(html, { sanitize: false })
      .process(matterResult.content);
    const contentHtml = processedContent.toString();

    return {
      slug,
      lang,
      title: frontMatter.title,
      description: frontMatter.description,
      content: contentHtml,
      date: frontMatter.date,
      category: frontMatter.category,
      readTime: frontMatter.readTime || calculateReadTime(matterResult.content),
      author: frontMatter.author,
      tags: frontMatter.tags,
      featured: frontMatter.featured,
    };
  } catch (error) {
    console.error(`Error reading blog post ${slug}:`, error);
    return null;
  }
}

export function getAllSlugs(): string[] {
  try {
    const fileNames = fs.readdirSync(postsDirectory);
    const slugs = fileNames
      .filter((fileName) => fileName.endsWith('.md'))
      .map((fileName) => {
        const slug = fileName.replace(/\.md$/, '');
        // Remove language suffix to get clean slug
        return slug.replace(/\.[a-z]{2}$/, '');
      })
      .filter((slug, index, array) => array.indexOf(slug) === index); // Remove duplicates

    return slugs;
  } catch (error) {
    console.error('Error reading blog slugs:', error);
    return [];
  }
}

export async function getPostsByCategory(category: string, lang: Locale): Promise<BlogPostMeta[]> {
  const posts = await getAllPosts(lang);
  return posts.filter((post) => post.category === category);
}

export async function getFeaturedPosts(lang: Locale): Promise<BlogPostMeta[]> {
  const posts = await getAllPosts(lang);
  return posts.filter((post) => post.featured === true);
}

function calculateReadTime(content: string): number {
  const wordsPerMinute = 200;
  const words = content.trim().split(/\s+/).length;
  return Math.ceil(words / wordsPerMinute);
}
