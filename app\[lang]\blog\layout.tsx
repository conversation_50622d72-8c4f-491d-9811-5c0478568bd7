import { getDictionary } from '@/lib/i18n/dictionaries'
import type { LayoutProps } from '@/types/i18n'
import BlogHeaderStatic from '@/components/blog/blog-header-static'

export default async function BlogLayout({ children, params }: LayoutProps) {
  const { lang } = await params
  const dict = await getDictionary(lang)

  return (
    <section className="flex flex-col min-h-screen">
      <BlogHeaderStatic lang={lang} dict={dict} />
      <div className="pt-20">
        {children}
      </div>
    </section>
  )
}
