# i18n 国际化设置说明

## 概述

本项目已为 `app/(dashboard)/` 文件夹实现了基本的 i18n 国际化准备，支持中英文两种语言。使用 NextJS 15 内置功能，无需额外的第三方库。

## 文件结构

```
├── app/
│   ├── [lang]/                          # 语言路由组
│   │   ├── layout.tsx                   # 语言特定布局
│   │   └── (dashboard)/                 # 仪表板路由组
│   │       ├── layout.tsx               # 仪表板布局（支持 i18n）
│   │       ├── page.tsx                 # 主页（支持 i18n）
│   │       ├── pricing/
│   │       │   └── page.tsx             # 定价页（支持 i18n）
│   │       └── profile/
│   │           └── page.tsx             # 个人资料页（支持 i18n）
│   ├── layout.tsx                       # 根布局
│   └── page.tsx                         # 根页面（重定向到默认语言）
├── components/
│   └── language-switcher.tsx            # 语言切换器组件
├── dictionaries/                        # 翻译字典
│   ├── en.json                          # 英文翻译
│   └── zh.json                          # 中文翻译
├── lib/
│   └── i18n/
│       ├── config.ts                    # i18n 配置
│       ├── dictionaries.ts              # 字典加载器
│       └── utils.ts                     # i18n 工具函数
└── middleware.ts                        # 中间件（语言检测和重定向）
```

## 主要功能

### 1. 自动语言检测
- 基于浏览器的 `Accept-Language` 头部自动检测用户语言
- 支持中文（zh）和英文（en）
- 默认语言为英文

### 2. URL 路由
- 所有路由都包含语言前缀：`/en/...` 或 `/zh/...`
- 访问根路径 `/` 会自动重定向到 `/en` 或 `/zh`
- 访问不带语言前缀的路径会自动添加语言前缀

### 3. 语言切换
- 页面头部包含语言切换器
- 切换语言时保持当前页面路径
- 使用 Select 组件提供良好的用户体验

### 4. 服务端渲染支持
- 所有翻译在服务端完成
- 使用 `getDictionary` 函数获取翻译内容
- 支持 NextJS 15 的 App Router

## 使用方法

### 1. 在页面组件中使用翻译

```tsx
import { getDictionary } from '@/lib/i18n/dictionaries';
import type { Locale } from '@/lib/i18n/config';

interface PageProps {
  params: { lang: Locale };
}

export default async function Page({ params }: PageProps) {
  const dict = await getDictionary(params.lang);
  
  return (
    <div>
      <h1>{dict.dashboard.title}</h1>
      <p>{dict.dashboard.description}</p>
    </div>
  );
}
```

### 2. 在布局组件中使用翻译

```tsx
import { getDictionary } from '@/lib/i18n/dictionaries';
import type { Locale } from '@/lib/i18n/config';

interface LayoutProps {
  children: React.ReactNode;
  params: { lang: Locale };
}

export default async function Layout({ children, params }: LayoutProps) {
  const dict = await getDictionary(params.lang);
  
  return (
    <div>
      <nav>
        <Link href={`/${params.lang}/dashboard`}>
          {dict.navigation.dashboard}
        </Link>
      </nav>
      {children}
    </div>
  );
}
```

### 3. 添加新的翻译

在 `dictionaries/en.json` 和 `dictionaries/zh.json` 中添加新的翻译键值对：

```json
// dictionaries/en.json
{
  "newSection": {
    "title": "New Section",
    "description": "This is a new section"
  }
}

// dictionaries/zh.json
{
  "newSection": {
    "title": "新章节",
    "description": "这是一个新章节"
  }
}
```

### 4. 生成本地化链接

```tsx
import Link from 'next/link';

// 在组件中
<Link href={`/${params.lang}/pricing`}>
  {dict.navigation.pricing}
</Link>
```

## 配置

### 支持的语言

在 `lib/i18n/config.ts` 中配置支持的语言：

```ts
export const i18n = {
  defaultLocale: 'en',
  locales: ['en', 'zh'],
} as const
```

### 中间件配置

在 `middleware.ts` 中配置路由匹配规则：

```ts
export const config = {
  matcher: [
    '/((?!_next|api|favicon.ico).*)',
  ],
};
```

## 注意事项

1. **现有路由迁移**：原有的 `app/(dashboard)/` 路由已被新的 `app/[lang]/(dashboard)/` 路由替代
2. **链接更新**：所有内部链接都需要包含语言前缀
3. **SEO 友好**：URL 包含语言信息，有利于搜索引擎优化
4. **类型安全**：使用 TypeScript 确保语言类型安全

## 下一步

1. 将现有组件中的硬编码文本替换为翻译键
2. 为更多页面添加翻译支持
3. 考虑添加更多语言支持
4. 实现更复杂的翻译功能（如复数形式、日期格式化等）

## 测试

访问以下 URL 测试 i18n 功能：

- `/` - 自动重定向到默认语言
- `/en` - 英文主页
- `/zh` - 中文主页
- `/en/pricing` - 英文定价页
- `/zh/pricing` - 中文定价页
- `/en/profile` - 英文个人资料页
- `/zh/profile` - 中文个人资料页
