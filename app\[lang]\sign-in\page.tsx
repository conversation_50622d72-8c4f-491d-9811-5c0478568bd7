import { LoginForm } from '@/components/auth/login-form';
import { getDictionary } from '@/lib/i18n/dictionaries';
import type { PageProps } from '@/types/i18n';

export default async function LoginPage({ params }: PageProps) {
  const { lang } = await params;
  const dict = await getDictionary(lang);

  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-8 bg-background">
      <div className="w-full max-w-md">
        <LoginForm lang={lang} dict={dict} />
      </div>
    </div>
  );
}
