# Mind Elixir

桌面端思维导图应用 Mind Elixir

## feature card

- 数据存本地，绝不跟踪用户数据
- 无广告
- 拥抱 ai，自带 MCP Server，支持 AI 生成内容

## feature bento

- 体积小得离谱，不足 10m（tauri 驱动）
- 多种导出格式：JSON,PNG,JPEG,WEBP,HTML,MD
- outline 模式，换个角度组织语言
- 支持 AI 生成内容

## 视频展示 AI 使用

- 通过 MCP 更新思维导图
- AIGC

（此功能需要联网）

## 开源内核

https://github.com/SSShooter/mind-elixir-core/releases/new

## 价格

无会员最高创建 10 个思维导图

- 基础免费
- 年订阅 29usd
- 终身版 39usd 限时 8 折

## 要求

- 语言为英文
- 不能捏造 feature
- 尽量专业，包含应用场景，faq 等模块
- 尽量吸引眼球
- 尽量减少冗余代码，相同模块的内容可以抽象出组件，使用数组 + map 渲染
