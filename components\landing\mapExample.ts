import { NodeObj } from 'mind-elixir'

export const mindMapExample: NodeObj = {
  topic: 'Boost Productivity with Mind Elixir',
  id: 'root-node',
  children: [
    {
      topic: 'AI-Powered Features',
      id: 'ai-features',
      style: {
        fontSize: '18px',
        fontWeight: 'bold',
      },
      children: [
        {
          topic: 'Automatic Idea Generation',
          id: 'auto-ideas',
          style: {
            fontSize: '16px',
          },
          tags: ['AI', 'Brainstorming'],
          hyperLink: 'https://github.com/SSShooter/mind-elixir-core',
        },
        {
          topic: 'Update Mind Map with Suggestions',
          id: 'update-map',
          style: {
            fontSize: '16px',
          },
          tags: ['AI', 'Smart Suggestions'],
        },
      ],
    },
    {
      topic: 'Core Features',
      id: 'core-features',
      style: {
        fontSize: '18px',
        fontWeight: 'bold',
      },
      children: [
        {
          topic: 'Lightweight (Under 10MB)',
          id: 'lightweight',
          style: {
            fontSize: '16px',
          },
          tags: ['Performance', 'Speed'],
        },
        {
          topic: 'No Ads, No Distractions',
          id: 'no-ads',
          style: {
            fontSize: '16px',
          },
          tags: ['Focus', 'Productivity'],
        },
        {
          topic: 'Multiple Export Formats',
          id: 'export-formats',
          style: {
            fontSize: '16px',
          },
          tags: ['Export', 'Customization'],
          children: [
            {
              topic: 'PNG, JPEG, MD, HTML',
              id: 'formats',
              style: {
                fontSize: '14px',
              },
            },
          ],
        },
      ],
    },
    {
      topic: 'Create & Share Ideas',
      id: 'create-share-ideas',
      style: {
        fontSize: '18px',
        fontWeight: 'bold',
      },
      children: [
        {
          topic: 'Mind Mapping for Collaboration',
          id: 'collaboration',
          style: {
            fontSize: '16px',
          },
          tags: ['Collaboration', 'Teamwork'],
          hyperLink: 'https://www.mind-elixir.com',
        },
        {
          topic: 'Export to Share',
          id: 'export-share',
          style: {
            fontSize: '16px',
          },
          tags: ['Sharing', 'Export'],
        },
      ],
    },
    {
      topic: 'Open Source & Community',
      id: 'open-source',
      style: {
        fontSize: '18px',
        fontWeight: 'bold',
      },
      children: [
        {
          topic: 'Contribute to the Code',
          id: 'contribute',
          style: {
            fontSize: '16px',
          },
          tags: ['Open Source', 'Community'],
          hyperLink: 'https://github.com/SSShooter/mind-elixir-core',
        },
      ],
    },
  ],
}
