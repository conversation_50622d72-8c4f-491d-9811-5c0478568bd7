import clientPromise, { dbName } from '../mongo'

export interface DbPayment {
  _id: string
  userId: string
  customerId: string
  sessionId: string
  subscriptionId: string
  paymentIntent: string
  type: 'lifetime' | 'annual'
  createdAt: Date
}

export async function createPayment(paymentData: {
  userId: string
  customerId: string
  sessionId: string
  subscriptionId: string
  paymentIntent: string
  type: DbPayment['type']
  createdAt: Date
}): Promise<DbPayment> {
  const client = await clientPromise
  const collection = client.db(dbName).collection('payments')

  const result = await collection.insertOne(paymentData)

  if (!result.acknowledged) {
    throw new Error('Failed to create payment')
  }

  return {
    _id: result.insertedId.toString(),
    ...paymentData,
  }
}

export async function getUserPayments(userId: string): Promise<DbPayment[]> {
  const client = await clientPromise
  const collection = client.db(dbName).collection('payments')

  const payments = await collection
    .find({ userId })
    .sort({ createdAt: -1 })
    .toArray()

  return payments as unknown as DbPayment[]
}
