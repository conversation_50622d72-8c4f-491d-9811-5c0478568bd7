import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { getCurrentUser } from '@/lib/get-session'
import { getUserPayments } from '@/lib/mongo/payment'
import { getUser } from '@/lib/mongo/user'
import {
  CalendarIcon,
  CheckCircle,
  CreditCard,
  Crown,
  XCircle,
  AlertCircle,
} from 'lucide-react'
import { formatDate } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import { CopyButton } from '@/components/CopyButton'
import { initUser } from '@/lib/payments/actions'
import { MemberBadge } from '@/components/MemberBadge'
import UserProfileAvatar from '@/components/UserProfileAvatar'
import { SubscriptionToggle } from '@/components/SubscriptionToggle'
import { getDictionary } from '@/lib/i18n/dictionaries'
import { getSubscriptionStatus } from '@/lib/payments/stripe'
import type { PageProps } from '@/types/i18n'

export default async function ProfilePage({ params }: PageProps) {
  const { lang } = await params
  const dict = await getDictionary(lang)
  const session = await getCurrentUser()

  if (!session?.id) {
    return (
      <main className="w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-12 mt-16">
        <Card>
          <CardHeader>
            <CardTitle>{dict.profile.signInRequired.title}</CardTitle>
            <CardDescription>
              {dict.profile.signInRequired.description}
            </CardDescription>
          </CardHeader>
        </Card>
      </main>
    )
  }

  let user = await getUser(session.id)
  if (!user) {
    user = await initUser(session)
  }
  const payments = await getUserPayments(session.id)

  // Get subscription statuses from Stripe for annual subscriptions
  const subscriptionStatuses = new Map()
  for (const payment of payments) {
    if (payment.type === 'annual' && payment.subscriptionId) {
      const status = await getSubscriptionStatus(payment.subscriptionId)
      if (status) {
        subscriptionStatuses.set(payment.subscriptionId, status)
      }
    }
  }

  return (
    <main className="mx-auto px-4 sm:px-6 lg:px-8 py-12 mt-16">
      <div className="grid md:grid-cols-1 gap-8 mx-auto">
        <Card className="w-3xl">
          <CardHeader>
            <CardTitle className="text-2xl">{dict.profile.title}</CardTitle>
            <CardDescription>
              {dict.profile.userInfo.accountInfo}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div className="flex items-center gap-4">
                {user?.image && (
                  <UserProfileAvatar
                    image={user.image}
                    name={user.name}
                    type={user.type}
                    size="lg"
                  />
                )}
                <div>
                  <h3 className="text-xl font-medium mb-2">
                    {user?.name}
                    {user?.type && (
                      <MemberBadge className="ml-2" type={user.type} />
                    )}
                  </h3>
                  <p className="text-sm text-muted-foreground">{user?.email}</p>
                </div>
              </div>

              {/* Subscription Status Section */}
              <div className="border-t pt-6">
                <h4 className="text-lg font-medium mb-4 flex items-center gap-2">
                  <Crown className="h-5 w-5" />{' '}
                  {dict.profile.subscription.title}
                </h4>
                <div className="space-y-4">
                  {user?.type ? (
                    <div className="p-4 border rounded-lg bg-green-50 dark:bg-green-950/20 border-green-200 dark:border-green-800/30">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="flex items-center gap-2">
                            <CheckCircle className="h-4 w-4 text-green-500 dark:text-green-400" />
                            <span className="font-medium text-green-800 dark:text-green-200">
                              {user.type === 'lifetime'
                                ? dict.profile.subscription.lifetimeMember
                                : dict.profile.subscription.annualSubscription}
                            </span>
                          </div>
                          {user.type === 'annual' && user.exp && (
                            <p className="text-sm text-green-700 dark:text-green-300">
                              {dict.profile.subscription.expires}:{' '}
                              {formatDate(new Date(user.exp))}
                            </p>
                          )}
                          {user.type === 'lifetime' && (
                            <p className="text-sm text-green-700 dark:text-green-300">
                              {dict.profile.subscription.lifetimeAccess}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="p-4 border rounded-lg bg-muted">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-foreground mb-1">
                            {dict.profile.subscription.freeUser}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {dict.profile.subscription.upgradePrompt}
                          </p>
                        </div>
                        <Button size="sm" asChild>
                          <Link href={`/${lang}/pricing`}>
                            {dict.profile.subscription.upgrade}
                          </Link>
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="border-t pt-6">
                <h4 className="text-lg font-medium mb-4 flex items-center gap-2">
                  <CreditCard className="h-5 w-5" />{' '}
                  {dict.profile.paymentHistory.title}
                </h4>
                {payments && payments.length > 0 ? (
                  <div className="space-y-4">
                    {payments.map((payment) => (
                      <div
                        key={payment._id}
                        className="p-4 border rounded-lg space-y-3"
                      >
                        {/* First row: Payment info */}
                        <div className="flex flex-col sm:flex-row sm:items-center justify-between">
                          <div className="space-y-1">
                            <div className="flex items-center gap-2">
                              {payment.type === 'annual' &&
                              payment.subscriptionId ? (
                                // For annual subscriptions, show icon based on status and cancelAtPeriodEnd
                                (() => {
                                  const subscriptionInfo =
                                    subscriptionStatuses.get(
                                      payment.subscriptionId
                                    )
                                  const status = subscriptionInfo?.status
                                  const cancelAtPeriodEnd =
                                    subscriptionInfo?.cancelAtPeriodEnd

                                  if (status === 'canceled') {
                                    return (
                                      <XCircle className="h-4 w-4 text-red-500" />
                                    )
                                  } else if (
                                    status === 'active' &&
                                    cancelAtPeriodEnd
                                  ) {
                                    // Active but will cancel at period end - show orange/warning color
                                    return (
                                      <AlertCircle className="h-4 w-4 text-orange-500" />
                                    )
                                  } else if (status === 'active') {
                                    return (
                                      <CheckCircle className="h-4 w-4 text-green-500" />
                                    )
                                  } else if (
                                    status === 'past_due' ||
                                    status === 'unpaid'
                                  ) {
                                    return (
                                      <AlertCircle className="h-4 w-4 text-yellow-500" />
                                    )
                                  } else {
                                    return (
                                      <CheckCircle className="h-4 w-4 text-green-500" />
                                    )
                                  }
                                })()
                              ) : (
                                // For lifetime purchases, always show green check
                                <CheckCircle className="h-4 w-4 text-green-500" />
                              )}
                              <span className="font-medium">
                                {payment.type.toUpperCase()}
                              </span>
                            </div>
                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                              <CalendarIcon className="h-3 w-3" />
                              {formatDate(payment.createdAt)}
                            </div>
                          </div>
                          {/* 100%折扣的订单没有payment.paymentIntent */}
                          {!(
                            payment.type === 'lifetime' &&
                            !payment.paymentIntent
                          ) && (
                            <div className="text-sm flex items-center gap-1 mt-2 sm:mt-0">
                              {dict.profile.paymentHistory.paymentId}:{' '}
                              {payment.type === 'lifetime'
                                ? payment.paymentIntent?.substring(0, 10)
                                : payment.subscriptionId?.substring(0, 10)}
                              ...
                              <CopyButton
                                value={
                                  payment.type === 'lifetime'
                                    ? payment.paymentIntent
                                    : payment.subscriptionId
                                }
                              />
                            </div>
                          )}
                        </div>

                        {/* Second row: Subscription toggle (only for annual subscriptions) */}
                        {payment.type === 'annual' &&
                          payment.subscriptionId && (
                            <div className="pt-4 border-t border-gray-100 dark:border-gray-800">
                              <SubscriptionToggle
                                subscriptionId={payment.subscriptionId}
                                initialCancelAtPeriodEnd={
                                  subscriptionStatuses.get(
                                    payment.subscriptionId
                                  )?.cancelAtPeriodEnd || false
                                }
                                subscriptionStatus={
                                  subscriptionStatuses.get(
                                    payment.subscriptionId
                                  )?.status
                                }
                                dict={dict.profile.subscriptionToggle}
                              />
                            </div>
                          )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-muted-foreground">
                    {dict.profile.paymentHistory.noHistory}
                    <Button variant="link" asChild>
                      <Link href={`/${lang}/pricing`}>
                        {dict.profile.paymentHistory.goToPricing}
                      </Link>
                    </Button>
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </main>
  )
}
