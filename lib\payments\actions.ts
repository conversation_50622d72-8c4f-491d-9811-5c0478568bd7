'use server'

import { createCheckoutSession, stripe } from './stripe'
import { withUser } from '@/lib/auth/middleware'
import {
  upsertUser,
  updateUserStripeCustomerId,
  DbUser,
  getUser,
  updateUserPayType,
  updateUserCancelAtPeriodEnd,
} from '../mongo/user'
import { getUserPayments } from '../mongo/payment'
import { getCurrentUser } from '../get-session'
import { MeUser } from '@/auth'

export const initUser = async (u: MeUser) => {
  let dbUser: DbUser = await upsertUser(u)
  if (!dbUser.stripeCustomerId) {
    const customer = await stripe.customers.create({
      email: dbUser.email as string,
      name: dbUser.name as string,
      metadata: { user_id: dbUser.uuid },
    })
    dbUser = await updateUserStripeCustomerId(dbUser.uuid, customer.id)
  }
  return dbUser
}
export const checkoutAction = withUser(async (formData, user) => {
  const u = user as Required<MeUser>
  let dbUser: DbUser = await initUser(u)
  const priceId = formData.get('priceId') as string
  const type = formData.get('type') as 'one_time' | 'recurring'
  const useCode = formData.get('useCode')
  await createCheckoutSession({
    user: dbUser,
    priceId,
    type,
    useCode: Boolean(useCode),
  })
})

export const cancelSubscriptionAction = async () => {
  try {
    // Get current user
    const session = await getCurrentUser()
    if (!session?.id) {
      return { success: false, error: 'Unauthorized' }
    }

    // Get user information
    const user = await getUser(session.id)
    if (!user) {
      return { success: false, error: 'User not found' }
    }

    // Check if user has annual subscription
    if (user.type !== 'annual') {
      return { success: false, error: 'No active annual subscription found' }
    }

    // Get user's payment records to find subscription ID
    const payments = await getUserPayments(session.id)
    const annualPayment = payments.find((p) => p.subscriptionId)

    if (!annualPayment || !annualPayment.subscriptionId) {
      return { success: false, error: 'Subscription ID not found' }
    }

    // Cancel Stripe subscription
    const subscription = await stripe.subscriptions.update(
      annualPayment.subscriptionId,
      {
        cancel_at_period_end: true,
      }
    )

    // Update user status - set type to null to indicate cancellation
    await updateUserPayType(session.id, null)

    return {
      success: true,
      message: 'Subscription cancelled successfully',
      subscription: {
        id: subscription.id,
        status: subscription.status,
        canceledAt: subscription.canceled_at,
      },
    }
  } catch (error) {
    console.error('Error in cancelSubscriptionAction:', error)
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : 'Failed to cancel subscription',
    }
  }
}

export const toggleSubscriptionAction = async (subscriptionId: string) => {
  try {
    // Get current user
    const session = await getCurrentUser()
    if (!session?.id) {
      return { success: false, error: 'Unauthorized' }
    }

    // Get user information
    const user = await getUser(session.id)
    if (!user) {
      return { success: false, error: 'User not found' }
    }

    // Note: We don't check user.type here because a user might have purchased
    // a lifetime membership after having an annual subscription, but they should
    // still be able to manage their previous annual subscription

    // Get current subscription from Stripe
    const subscription = await stripe.subscriptions.retrieve(subscriptionId)

    if (!subscription) {
      return { success: false, error: 'Subscription not found' }
    }

    // Toggle cancel_at_period_end
    const newCancelAtPeriodEnd = !subscription.cancel_at_period_end

    const updatedSubscription = await stripe.subscriptions.update(
      subscriptionId,
      {
        cancel_at_period_end: newCancelAtPeriodEnd,
      }
    )

    // Update user's cancelAtPeriodEnd status in database
    await updateUserCancelAtPeriodEnd(session.id, newCancelAtPeriodEnd)

    return {
      success: true,
      message: newCancelAtPeriodEnd
        ? 'Subscription will be cancelled at period end'
        : 'Subscription cancellation has been reverted',
      subscription: {
        id: updatedSubscription.id,
        status: updatedSubscription.status,
        cancelAtPeriodEnd: updatedSubscription.cancel_at_period_end,
        currentPeriodEnd: (updatedSubscription as any).current_period_end,
      },
    }
  } catch (error) {
    console.error('Error in toggleSubscriptionAction:', error)
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : 'Failed to toggle subscription',
    }
  }
}
