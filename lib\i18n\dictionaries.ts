import 'server-only'
import type { Locale } from './config'
import type { Dictionary } from '@/types/i18n'

const dictionaries = {
  en: () => import('../../dictionaries/en.json').then((module) => module.default),
  zh: () => import('../../dictionaries/zh.json').then((module) => module.default),
}

export const getDictionary = async (locale: Locale): Promise<Dictionary> => {
  return dictionaries[locale]?.() ?? dictionaries.en()
}
