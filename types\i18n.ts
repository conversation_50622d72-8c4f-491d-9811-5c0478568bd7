import type { Locale } from '@/lib/i18n/config';

export interface PageProps {
  params: Promise<{ lang: Locale }>;
}

export interface LayoutProps {
  children: React.ReactNode;
  params: Promise<{ lang: Locale }>;
}

export interface Dictionary {
  navigation: {
    feature: string;
    'ai-powered': string;
    download: string;
    pricing: string;
    faq: string;
    blog: string;
  };
  common: {
    loading: string;
    error: string;
    success: string;
    cancel: string;
    confirm: string;
    save: string;
    edit: string;
    delete: string;
    back: string;
    next: string;
    previous: string;
    close: string;
    open: string;
    signIn: string;
    signOut: string;
    profile: string;
  };
  dashboard: {
    title: string;
    welcome: string;
    description: string;
  };
  pricing: {
    title: string;
    description: string;
  };
  blog: {
    title: string;
    description: string;
    readMore: string;
    backToBlog: string;
    publishedOn: string;
    readTime: string;
    noPostsFound: string;
    categories: {
      all: string;
      updates: string;
      tutorials: string;
      tips: string;
    };
    meta: {
      title: string;
      description: string;
    };
  };
  profile: {
    title: string;
    description: string;
    signInRequired: {
      title: string;
      description: string;
    };
    userInfo: {
      accountInfo: string;
    };
    subscription: {
      title: string;
      lifetimeMember: string;
      annualSubscription: string;
      expires: string;
      lifetimeAccess: string;
      freeUser: string;
      upgradePrompt: string;
      upgrade: string;
    };
    paymentHistory: {
      title: string;
      noHistory: string;
      goToPricing: string;
      paymentId: string;
    };
    cancelSubscription: {
      buttonText: string;
      dialogTitle: string;
      dialogDescription: string;
      keepSubscription: string;
      confirmCancellation: string;
      cancelling: string;
      errorDefault: string;
    };
    subscriptionToggle: {
      label: string;
      description: string;
      cancelledLabel: string;
      cancelledDescription: string;
      statusCanceled: string;
      statusActive: string;
      statusPastDue: string;
      statusUnpaid: string;
      errorDefault: string;
    };
  };
  features: {
    sectionTitle: string;
    dataPrivacy: {
      title: string;
      description: string;
    };
    adFree: {
      title: string;
      description: string;
    };
    aiIntegration: {
      title: string;
      description: string;
    };
    tagOrganization: {
      title: string;
      description: string;
    };
  };
  featureBento: {
    sectionTitle: string;
    lightweight: {
      name: string;
      description: string;
      cta: string;
    };
    exportOptions: {
      name: string;
      description: string;
      cta: string;
    };
    outlineMode: {
      name: string;
      description: string;
      cta: string;
    };
    aiPowered: {
      name: string;
      description: string;
      cta: string;
    };
  };
  aiSection: {
    sectionTitle: string;
    tabs: {
      aigc: string;
      mcp: string;
    };
    videoAlt: {
      aigc: string;
      mcp: string;
    };
  };
  pricingSection: {
    sectionTitle: string;
    sectionDescription: string;
    features: {
      privateStorage: string;
      outlineMode: string;
      allExportFormats: string;
      aiContentGeneration: string;
      adFreeExperience: string;
      unlimitedMindMaps: string;
      lifetimeUpdates: string;
    };
    tiers: {
      free: {
        name: string;
        description: string;
        buttonText: string;
      };
      annual: {
        name: string;
        description: string;
        buttonText: string;
      };
      lifetime: {
        name: string;
        description: string;
        buttonText: string;
        discount: string;
      };
    };
    withoutDiscount: string;
  };
  downloadSection: {
    sectionTitle: string;
    sectionDescription: string;
    appName: string;
    latestVersion: string;
    downloadNow: string;
    availableForAllPlatforms: string;
    recommended: string;
    moreOptions: string;
    otherVersions: string;
    platforms: {
      windows: {
        name: string;
        version: string;
      };
      macos: {
        name: string;
        version: string;
      };
      linux: {
        name: string;
        version: string;
      };
    };
    status: {
      stable: string;
      beta: string;
    };
    poweredByOpenSource: string;
  };
  faqSection: {
    sectionTitle: string;
    sectionDescription: string;
    faqs: Array<{
      question: string;
      answer: string;
    }>;
  };
  auth: {
    signIn: {
      title: string;
      continueWithGitHub: string;
      continueWithGoogle: string;
      continueWithLinkedIn: string;
      termsAndPrivacy: string;
    };
  };
  heroSection: {
    title: {
      line1: string;
      line2: string;
    };
    description: {
      line1: string;
      line2: string;
    };
    buttons: {
      downloadNow: string;
      learnMore: string;
    };
  };
  meta: {
    title: string;
    description: string;
    keywords: string;
    openGraph: {
      title: string;
      description: string;
      siteName: string;
    };
    twitter: {
      title: string;
      description: string;
    };
  };
  footer: {
    appName: string;
    description: string;
    sections: {
      product: string;
      feedback: string;
      legal: string;
      followUs: string;
    };
    navigation: {
      product: Array<{
        name: string;
        href: string;
      }>;
      legal: Array<{
        name: string;
        href: string;
      }>;
      feedback: Array<{
        name: string;
        href: string;
      }>;
    };
    socialLinks: Array<{
      label: string;
      href: string;
    }>;
    copyright: string;
  };
}
