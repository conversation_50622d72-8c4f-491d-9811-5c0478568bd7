import Stripe from 'stripe'
import { redirect } from 'next/navigation'

import { DbUser } from '../mongo/user'
import { i18n } from '@/lib/i18n/config'

export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-03-31.basil',
})

export async function createCheckoutSession({
  user,
  priceId,
  type,
  useCode,
}: {
  user: DbUser
  priceId: string
  type: 'one_time' | 'recurring'
  useCode?: boolean
}) {
  const promoCodes = await stripe.promotionCodes.list({
    code: 'LAUNCH',
    limit: 1,
  })
  const promoCodeId = promoCodes.data[0]?.id
  const session = await stripe.checkout.sessions.create({
    payment_method_types: ['card'],
    line_items: [
      {
        price: priceId,
        quantity: 1,
      },
    ],
    mode: type === 'one_time' ? 'payment' : 'subscription',
    discounts: useCode ? [{ promotion_code: promoCodeId }] : undefined,
    allow_promotion_codes: useCode ? undefined : true,
    success_url: `${process.env.BASE_URL}/api/stripe/checkout?session_id={CHECKOUT_SESSION_ID}`,
    cancel_url: `${process.env.BASE_URL}/${i18n.defaultLocale}/pricing`,
    customer: user.stripeCustomerId,
    client_reference_id: user.uuid,
  })

  redirect(session.url!)
}

export async function getStripePrices() {
  const prices = await stripe.prices.list({
    expand: ['data.product'],
    active: true,
    type: 'recurring',
  })

  return prices.data.map((price) => ({
    id: price.id,
    productId:
      typeof price.product === 'string' ? price.product : price.product.id,
    unitAmount: price.unit_amount,
    currency: price.currency,
    interval: price.recurring?.interval,
    trialPeriodDays: price.recurring?.trial_period_days,
  }))
}

export async function getStripeProducts() {
  const products = await stripe.products.list({
    active: true,
    expand: ['data.default_price'],
  })
  console.log(products)
  const data = products.data
  return data.map((product) => {
    const price = product.default_price as Stripe.Price
    return {
      id: product.id,
      name: product.name,
      description: product.description,
      defaultPriceId: price.id,
      type: price.type,
      currency: price.currency,
      unitAmount: price.unit_amount,
    }
  })
}

export async function getSubscriptionStatus(subscriptionId: string) {
  try {
    const subscription = await stripe.subscriptions.retrieve(subscriptionId)
    return {
      id: subscription.id,
      status: subscription.status,
      cancelAtPeriodEnd: subscription.cancel_at_period_end,
      currentPeriodEnd: (subscription as any).current_period_end,
      currentPeriodStart: (subscription as any).current_period_start,
    }
  } catch (error) {
    console.error('Error retrieving subscription:', error)
    return null
  }
}
