import { <PERSON>R<PERSON> } from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { MindMap } from './mind-map'
import { HeroScrollEffect } from './hero-scroll-effect'
import { getDictionary } from '@/lib/i18n/dictionaries'
import type { Locale } from '@/lib/i18n/config'

interface HeroSectionProps {
  lang: Locale
}

export const HeroSection = async ({ lang }: HeroSectionProps) => {
  const dict = await getDictionary(lang)
  return (
    <div className="relative h-screen w-screen overflow-hidden bg-gradient-to-b from-purple-50 to-white dark:from-darkblue dark:to-background">
      <HeroScrollEffect />
      <MindMap className="absolute left-0 -bottom-8 z-10 [mask-image:linear-gradient(to_top,transparent_10%,#000_100%)]" />
      <div
        id="hero"
        className="absolute left-0 -top-24 h-screen w-screen z-20 min-h-screen flex items-center justify-center "
      >
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12 text-center">
          <h1 className="text-5xl md:text-6xl font-bold tracking-tight text-gray-900 dark:text-gray-100 mb-6">
            {dict.heroSection.title.line1}
            <span className="text-purple-600 dark:text-blue-300 block mt-2">
              {dict.heroSection.title.line2}
            </span>
          </h1>
          <div className="text-xl text-gray-600 dark:text-gray-400 mb-8 max-w-3xl mx-auto">
            <p>{dict.heroSection.description.line1}</p>
            <p>{dict.heroSection.description.line2}</p>
          </div>
          <div className="flex justify-center gap-4">
            <a href={`#download`}>
              <Button size="lg">
                {dict.heroSection.buttons.downloadNow}{' '}
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </a>
            <a href={`#feature`}>
              <Button size="lg" variant="outline">
                {dict.heroSection.buttons.learnMore}
              </Button>
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
