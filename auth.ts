import NextAuth, { DefaultSession } from 'next-auth'
import Google from 'next-auth/providers/google'
import GitHub from 'next-auth/providers/github'
import LinkedIn from 'next-auth/providers/linkedin'

export type MeUser = {
  provider: string
  providerAccountId: string
  /**
   * By default, TypeScript merges new interface properties and overwrites existing ones.
   * In this case, the default session user properties will be overwritten,
   * with the new ones defined above. To keep the default session user properties,
   * you need to add them back into the newly declared interface.
   */
} & DefaultSession['user']

declare module 'next-auth' {
  /**
   * Returned by `auth`, `useSession`, `getSession` and received as a prop on the `SessionProvider` React Context
   */
  interface Session {
    user: MeUser
  }
}
export const { handlers, signIn, signOut, auth } = NextAuth({
  providers: [Google, GitHub, LinkedIn],
  callbacks: {
    async jwt({ token, user, account, profile }) {
      if (user && account) {
        // debugger
        // user, account, profile is only available during sign-in
        token.id = user.id
        token.p = account.provider
        token.pid = account.providerAccountId
      }
      return token
    },
    session({ session, token }) {
      session.user.id = token.p + ':' + token.pid
      session.user.provider = token.p as string
      session.user.providerAccountId = token.pid as string
      return session
    },
  },
})
