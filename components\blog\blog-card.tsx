import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar, Clock } from 'lucide-react';
import type { BlogPostMeta } from '@/types/blog';
import type { Locale } from '@/lib/i18n/config';
import type { Dictionary } from '@/types/i18n';
import { formatDate } from '@/lib/date-utils';

interface BlogCardProps {
  post: BlogPostMeta;
  lang: Locale;
  dict: Dictionary;
}

export function BlogCard({ post, lang, dict }: BlogCardProps) {
  const categoryColors = {
    updates: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
    tutorials: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    tips: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
  };

  return (
    <Card className="h-full hover:shadow-lg transition-shadow duration-200">
      <CardHeader>
        <div className="flex items-center justify-between mb-2">
          <Badge
            variant="secondary"
            className={categoryColors[post.category]}
          >
            {dict.blog.categories[post.category]}
          </Badge>
          {post.featured && (
            <Badge variant="default" className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300">
              Featured
            </Badge>
          )}
        </div>
        <CardTitle className="line-clamp-2">
          <Link
            href={`/${lang}/blog/${post.slug}`}
            className="hover:text-primary transition-colors"
          >
            {post.title}
          </Link>
        </CardTitle>
        <CardDescription className="line-clamp-3">
          {post.description}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1">
              <Calendar className="h-4 w-4" />
              <span>{formatDate(post.date, lang)}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Clock className="h-4 w-4" />
              <span>{post.readTime} {dict.blog.readTime}</span>
            </div>
          </div>
        </div>
        {post.author && (
          <div className="mt-2 text-sm text-muted-foreground">
            {post.author}
          </div>
        )}
        {post.tags && post.tags.length > 0 && (
          <div className="mt-3 flex flex-wrap gap-1">
            {post.tags.slice(0, 3).map((tag) => (
              <Badge key={tag} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
            {post.tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{post.tags.length - 3}
              </Badge>
            )}
          </div>
        )}
        <div className="mt-4">
          <Link
            href={`/${lang}/blog/${post.slug}`}
            className="text-primary hover:underline text-sm font-medium"
          >
            {dict.blog.readMore} →
          </Link>
        </div>
      </CardContent>
    </Card>
  );
}
