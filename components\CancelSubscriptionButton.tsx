'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { Loader2, X } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { cancelSubscriptionAction } from '@/lib/payments/actions'
import type { Dictionary } from '@/types/i18n'

interface CancelSubscriptionButtonProps {
  className?: string
  dict?: Dictionary['profile']['cancelSubscription']
}

// Default English translations for backward compatibility
const defaultDict = {
  buttonText: "Cancel Subscription",
  dialogTitle: "Confirm Cancellation",
  dialogDescription: "Are you sure you want to cancel your annual subscription? Cancellation will prevent automatic renewal, but you can continue using premium features until the current subscription period ends.",
  keepSubscription: "Keep Subscription",
  confirmCancellation: "Confirm Cancellation",
  cancelling: "Cancelling...",
  errorDefault: "Failed to cancel subscription"
}

export function CancelSubscriptionButton({ className, dict }: CancelSubscriptionButtonProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isOpen, setIsOpen] = useState(false)
  const router = useRouter()

  // Use provided dict or fall back to default
  const t = dict || defaultDict

  const handleCancelSubscription = async () => {
    setIsLoading(true)
    setError(null)

    try {
      const result = await cancelSubscriptionAction()

      if (!result.success) {
        throw new Error(result.error || t.errorDefault)
      }

      // 成功后关闭对话框并刷新页面
      setIsOpen(false)
      router.refresh()
    } catch (error) {
      console.error('Error cancelling subscription:', error)
      setError(error instanceof Error ? error.message : t.errorDefault)
      // 失败时不关闭对话框，让用户看到错误信息
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
      <AlertDialogTrigger asChild>
        <Button variant="destructive" size="sm" className={className}>
          <X className="h-4 w-4 mr-2" />
          {t.buttonText}
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{t.dialogTitle}</AlertDialogTitle>
          <AlertDialogDescription>
            {t.dialogDescription}
          </AlertDialogDescription>
        </AlertDialogHeader>
        {error && (
          <div className="text-sm text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800/30 p-3 rounded-md">
            {error}
          </div>
        )}
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isLoading}>
            {t.keepSubscription}
          </AlertDialogCancel>
          <Button
            onClick={handleCancelSubscription}
            disabled={isLoading}
            variant="destructive"
            className="bg-red-600 hover:bg-red-700 dark:bg-red-600/80 dark:hover:bg-red-600 text-white"
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                {t.cancelling}
              </>
            ) : (
              t.confirmCancellation
            )}
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
