import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { getDictionary } from '@/lib/i18n/dictionaries'
import { getPostBySlug, getAllSlugs } from '@/lib/blog'
import { BlogPostComponent } from '@/components/blog/blog-post'
import BlogHeaderStatic from '@/components/blog/blog-header-static'
import type { Locale } from '@/lib/i18n/config'

interface BlogPostPageProps {
  params: Promise<{
    lang: Locale
    slug: string
  }>
}

export async function generateStaticParams() {
  const slugs = getAllSlugs()
  const locales: Locale[] = ['en', 'zh']

  const params = []
  for (const slug of slugs) {
    for (const lang of locales) {
      params.push({ slug, lang })
    }
  }

  return params
}

export async function generateMetadata({
  params,
}: BlogPostPageProps): Promise<Metadata> {
  const { lang, slug } = await params
  const post = await getPostBySlug(slug, lang)

  if (!post) {
    return {
      title: 'Post Not Found',
    }
  }

  return {
    title: `${post.title} - Mind Elixir Blog`,
    description: post.description,
    openGraph: {
      title: post.title,
      description: post.description,
      type: 'article',
      publishedTime: post.date,
      authors: post.author ? [post.author] : undefined,
      tags: post.tags,
    },
    twitter: {
      card: 'summary_large_image',
      title: post.title,
      description: post.description,
    },
  }
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const { lang, slug } = await params
  const dict = await getDictionary(lang)
  const post = await getPostBySlug(slug, lang)

  if (!post) {
    notFound()
  }

  return (
    <section className="flex flex-col min-h-screen">
      <BlogHeaderStatic
        lang={lang}
        dict={dict}
        showBackButton={true}
        backHref={`/${lang}/blog/category/all`}
      />
      <div className="container mx-auto px-4 py-12">
        <BlogPostComponent post={post} lang={lang} dict={dict} />
      </div>
    </section>
  )
}
