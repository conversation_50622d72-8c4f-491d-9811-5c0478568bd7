import {
  BrainCir<PERSON>it,
  Feather,
  PackageOpen,
  TableOfContents,
} from 'lucide-react'
import { BentoCard, BentoGrid } from '@/components/magicui/bento-grid'
import { ExportTypes } from './export-types'
import { SectionTitle } from './section-title'
import { OutlinerShowcase } from './outliner-showcase'
import { NumberTicker } from '@/components/magicui/number-ticker'
import { Meteors } from '../magicui/meteors'
import { CpuArchitecture } from './cpu-architecture'
import { getDictionary } from '@/lib/i18n/dictionaries'
import type { Locale } from '@/lib/i18n/config'

interface FeatureBentoProps {
  lang: Locale
}

const UnderTen = ({ className }: { className?: string }) => {
  return (
    <div
      className={
        className +
        ' whitespace-pre-wrap w-full h-full text-4xl font-bold tracking-tighter text-black/80 dark:text-white/80 text-right'
      }
    >
      <Meteors angle={110} />
      {'<'}
      <NumberTicker startValue={99} value={10} />
      <span className="text-2xl">MB</span>
      <div className="absolute -bottom-2 left-0 right-0 h-2 bg-gradient-to-r from-blue-500/20 to-purple-500/20 blur-sm" />
    </div>
  )
}


export async function FeatureBento({ lang }: FeatureBentoProps) {
  const dict = await getDictionary(lang)

  const features = [
    {
      Icon: Feather,
      name: dict.featureBento.lightweight.name,
      description: dict.featureBento.lightweight.description,
      href: '#',
      cta: dict.featureBento.lightweight.cta,
      className: 'col-span-3 lg:col-span-1',
      background: (
        <UnderTen className="absolute right-10 top-10 opacity-80 group-hover:scale-110 transition-transform duration-300" />
      ),
    },
    {
      Icon: PackageOpen,
      name: dict.featureBento.exportOptions.name,
      description: dict.featureBento.exportOptions.description,
      href: '#',
      cta: dict.featureBento.exportOptions.cta,
      className: 'col-span-3 lg:col-span-2',
      background: (
        <ExportTypes className="absolute right-2 top-4  border-none transition-all duration-300 ease-out [mask-image:linear-gradient(to_top,transparent_10%,#000_100%)] group-hover:scale-90" />
      ),
    },
    {
      Icon: TableOfContents,
      name: dict.featureBento.outlineMode.name,
      description: dict.featureBento.outlineMode.description,
      href: '#',
      cta: dict.featureBento.outlineMode.cta,
      className: 'col-span-3 lg:col-span-2',
      background: (
        <OutlinerShowcase className="absolute right-0 top-0 opacity-60 [mask-image:linear-gradient(to_top,transparent_40%,#000_100%)]" />
      ),
    },
    {
      Icon: BrainCircuit,
      name: dict.featureBento.aiPowered.name,
      description: dict.featureBento.aiPowered.description,
      className: 'col-span-3 lg:col-span-1',
      href: '#',
      cta: dict.featureBento.aiPowered.cta,
      background: (
        <CpuArchitecture text="AI" className="absolute -right-20 -top-20" />
      ),
    },
  ]
  return (
    <div className="my-26 max-w-[80vw] m-auto">
      <SectionTitle
        title={dict.featureBento.sectionTitle}
        id="feature-bento"
      />
      <BentoGrid>
        {features.map((feature, idx) => (
          <BentoCard key={idx} {...feature} />
        ))}
      </BentoGrid>
    </div>
  )
}
