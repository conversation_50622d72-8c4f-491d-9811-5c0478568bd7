export interface BlogPost {
  slug: string;
  title: string;
  description: string;
  content: string;
  date: string;
  category: 'updates' | 'tutorials' | 'tips';
  readTime: number;
  author?: string;
  tags?: string[];
  featured?: boolean;
  lang: 'en' | 'zh';
}

export interface BlogPostMeta {
  slug: string;
  title: string;
  description: string;
  date: string;
  category: 'updates' | 'tutorials' | 'tips';
  readTime: number;
  author?: string;
  tags?: string[];
  featured?: boolean;
  lang: 'en' | 'zh';
}

export interface BlogFrontMatter {
  title: string;
  description: string;
  date: string;
  category: 'updates' | 'tutorials' | 'tips';
  readTime?: number;
  author?: string;
  tags?: string[];
  featured?: boolean;
}
