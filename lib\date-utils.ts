import type { Locale } from '@/lib/i18n/config';

export function formatDate(dateString: string, lang: Locale): string {
  const date = new Date(dateString);
  
  if (lang === 'zh') {
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  }
  
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
}
