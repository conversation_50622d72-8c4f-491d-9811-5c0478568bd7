'use client'
import { useEffect } from 'react'
import { mindMapExample } from './mapExample'
import { useIsMobile } from '@/hooks/use-mobile'

export const MindMap = ({ className }: { className: string }) => {
  const isMobile = useIsMobile()

  useEffect(() => {
    import('mind-elixir').then((MindElixir) => {
      const theme = MindElixir.default.DARK_THEME
      theme.cssVar['--bgcolor'] = 'rgba(0,0,0,0)'
      const mei = new MindElixir.default({
        el: '#map',
        direction: 2,
        nodeMenu: false,
        contextMenu: false,
        toolBar: false,
        theme,
      })
      mei.init({
        nodeData: mindMapExample,
      })
      mei.scale(isMobile ? 0.3 : 0.8)
      mei.toCenter()
      window.addEventListener('resize', () => {
        mei.toCenter()
      })
    })
  }, [isMobile])
  return (
    <div id="wrapper" className={className}>
      <div
        id="map"
        className="h-[50vh] w-screen pointer-events-none"
        onScroll={(e) => e.preventDefault()}
      ></div>
    </div>
  )
}
