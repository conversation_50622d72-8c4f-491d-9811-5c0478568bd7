import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion'
import { SectionTitle } from './section-title'
import { getDictionary } from '@/lib/i18n/dictionaries'
import type { Locale } from '@/lib/i18n/config'

interface FaqSectionProps {
  lang: Locale
}

export const FaqSection = async ({ lang }: FaqSectionProps) => {
  const dict = await getDictionary(lang)
  return (
    <div className="my-26 max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
      <SectionTitle
        id='faq'
        title={dict.faqSection.sectionTitle}
        description={dict.faqSection.sectionDescription}
      />
      <Accordion type="single" collapsible>
        {dict.faqSection.faqs.map((faq, index) => (
          <AccordionItem key={index} value={`item-${index}`}>
            <AccordionTrigger className="text-left">
              {faq.question}
            </AccordionTrigger>
            <AccordionContent>{faq.answer}</AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </div>
  )
}
