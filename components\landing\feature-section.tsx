import { MegaphoneOff, BotMessageSquare, ShieldUser, Tags } from 'lucide-react'
import { SectionTitle } from './section-title'
import { getDictionary } from '@/lib/i18n/dictionaries'
import type { Locale } from '@/lib/i18n/config'

interface FeatureSectionProps {
  lang: Locale
}

export const FeatureSection = async ({ lang }: FeatureSectionProps) => {
  const dict = await getDictionary(lang)

  const features = [
    {
      title: dict.features.dataPrivacy.title,
      description: dict.features.dataPrivacy.description,
      icon: <ShieldUser />,
    },
    {
      title: dict.features.aiIntegration.title,
      description: dict.features.aiIntegration.description,
      icon: <BotMessageSquare />,
    },
    {
      title: dict.features.tagOrganization.title,
      description: dict.features.tagOrganization.description,
      icon: <Tags />,
    },
    {
      title: dict.features.adFree.title,
      description: dict.features.adFree.description,
      icon: <MegaphoneOff />,
    },
  ]
  return (
    <div className="my-26 max-w-[80vw] m-auto">
      <SectionTitle title={dict.features.sectionTitle} id="feature" />
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mt-12">
        {features.map((item) => (
          <div
            key={item.title}
            style={{
              opacity: 1,
              filter: 'blur(0px)',
              willChange: 'auto',
              transform: 'translateY(-6px)',
            }}
          >
            <div className="rounded-lg border text-card-foreground bg-background border-none shadow-none">
              <div className="p-6 space-y-4">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                  {item.icon}
                </div>
                <h3 className="text-xl font-semibold">{item.title}</h3>
                <p className="text-muted-foreground">{item.description}</p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
