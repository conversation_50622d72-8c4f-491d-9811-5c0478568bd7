import { Check } from 'lucide-react'
import { SectionTitle } from './section-title'
import { ShineBorder } from '../magicui/shine-border'
import { getStripeProducts } from '@/lib/payments/stripe'
import { checkoutAction } from '@/lib/payments/actions'
import { SubmitButton } from '@/components/submit-button'
import { getDictionary } from '@/lib/i18n/dictionaries'
import type { Locale } from '@/lib/i18n/config'
import { Button } from '../ui/button'

interface PricingSectionProps {
  lang: Locale
}

export const PricingSection = async ({ lang }: PricingSectionProps) => {
  const dict = await getDictionary(lang)

  const features = [
    dict.pricingSection.features.privateStorage,
    dict.pricingSection.features.outlineMode,
    dict.pricingSection.features.allExportFormats,
    dict.pricingSection.features.aiContentGeneration,
    dict.pricingSection.features.adFreeExperience,
  ]

  const pricingTiers = [
    {
      name: dict.pricingSection.tiers.free.name,
      price: 0,
      description: dict.pricingSection.tiers.free.description,
      features: features,
      buttonText: dict.pricingSection.tiers.free.buttonText,
    },
    {
      name: dict.pricingSection.tiers.annual.name,
      type: 'recurring',
      price: 29,
      description: dict.pricingSection.tiers.annual.description,
      features: [...features, dict.pricingSection.features.unlimitedMindMaps],
      buttonText: dict.pricingSection.tiers.annual.buttonText,
      shineColor: ['#cccccc', '#dddddd', '#eeeeee'],
    },
    {
      name: dict.pricingSection.tiers.lifetime.name,
      type: 'one_time',
      price: 39,
      discount: 0.8,
      discountDesc: dict.pricingSection.tiers.lifetime.discount,
      description: dict.pricingSection.tiers.lifetime.description,
      features: [
        ...features,
        dict.pricingSection.features.unlimitedMindMaps,
        dict.pricingSection.features.lifetimeUpdates,
      ],
      buttonText: dict.pricingSection.tiers.lifetime.buttonText,
      shineColor: ['#A07CFE', '#FE8FB5', '#FFBE7B'],
    },
  ]
  const products = await getStripeProducts()
  const tiers = pricingTiers.map((item) => {
    const prod = products.find((p) => p.type === item.type)
    const price = prod?.unitAmount ? prod.unitAmount / 100 : item.price
    return {
      ...item,
      defaultPriceId: prod?.defaultPriceId,
      price,
      discountedPrice: item.discount
        ? (price * item.discount).toFixed(2)
        : undefined,
      currency: prod?.currency || 'usd',
    }
  })
  return (
    <div className="py-24">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <SectionTitle
          title={dict.pricingSection.sectionTitle}
          description={dict.pricingSection.sectionDescription}
          id="pricing"
        />

        <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
          {tiers.map((tier) => (
            <div
              key={tier.name}
              className={`relative  rounded-2xl border-2 flex flex-col justify-between `}
            >
              <ShineBorder shineColor={tier.shineColor} />
              <div className="p-8">
                <h3 className="text-2xl font-semibold ">{tier.name}</h3>
                <div className="mt-4 flex items-baseline">
                  <span className="text-4xl font-bold tracking-tight ">
                    {tier.discount ? tier.discountedPrice : tier.price}{' '}
                    {tier.currency}
                  </span>
                </div>
                {tier.discount && (
                  <div>
                    <span className="text-gray-500 line-through">
                      {tier.price} {tier.currency}
                    </span>
                    <span className="ml-2 text-green-600 font-semibold">
                      {tier.discountDesc}
                    </span>
                  </div>
                )}
                <p className={'text-gray-300 ' + (tier.discount ? '' : 'mt-6')}>
                  {tier.description}
                </p>

                <ul className="mt-8 space-y-4">
                  {tier.features.map((feature) => (
                    <li key={feature} className="flex items-center">
                      <Check className="h-5 w-5 text-green-500 mr-3" />
                      <span className="text-gray-400">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
              <div className="p-8 pt-0">
                {tier.discount && (
                  <form action={checkoutAction}>
                    <input
                      type="hidden"
                      name="priceId"
                      value={tier.defaultPriceId as string}
                    />
                    <input
                      type="hidden"
                      name="type"
                      value={tier.type as string}
                    />
                    <SubmitButton
                      variant="link"
                      text={dict.pricingSection.withoutDiscount}
                    />
                  </form>
                )}
                {tier.price === 0 ? (
                  <a href={'/' + lang + "/#download"}>
                    <Button className="w-full">{tier.buttonText}</Button>
                  </a>
                ) : (
                  <form action={checkoutAction}>
                    <input
                      type="hidden"
                      name="priceId"
                      value={tier.defaultPriceId as string}
                    />
                    <input
                      type="hidden"
                      name="type"
                      value={tier.type as string}
                    />
                    {tier.discount && (
                      <input type="hidden" name="useCode" value="true" />
                    )}
                    <SubmitButton text={tier.buttonText} />
                  </form>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
