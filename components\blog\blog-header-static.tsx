import Link from 'next/link'
import Image from 'next/image'
import { ArrowLeft, Home } from 'lucide-react'
import logo from '@/assets/mind-elixir-desktop.svg'
import type { Locale } from '@/lib/i18n/config'
import type { Dictionary } from '@/types/i18n'
import { Button } from '@/components/ui/button'

interface BlogHeaderStaticProps {
  lang: Locale
  dict: Dictionary
  showBackButton?: boolean
  backHref?: string
}

export default function BlogHeaderStatic({
  lang,
  dict,
  showBackButton = false,
  backHref = `/${lang}/blog/category/all`
}: BlogHeaderStaticProps) {
  const mainLinks = [
    { key: 'home', label: dict.common.back, href: `/${lang}`, icon: Home },
    { key: 'blog', label: dict.blog.title, href: `/${lang}/blog/category/all` },
  ]

  return (
    <header className="border-b fixed w-screen top-0 left-0 z-100 bg-white dark:bg-darkblue">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex justify-between items-center text-gray-900 dark:text-white">
        {/* Left side - Logo and Back Button */}
        <div className="flex items-center gap-4">
          {showBackButton && (
            <Link href={backHref}>
              <Button variant="ghost" size="sm" className="text-[#d0d1df] hover:text-gray-400">
                <ArrowLeft className="h-4 w-4 mr-2" />
                {dict.blog.backToBlog}
              </Button>
            </Link>
          )}

          <Link href={`/${lang}`} className="flex items-center">
            <Image src={logo} width={24} height={24} alt="MindElixir" />
            <span className="ml-2 text-xl">
              Mind Elixir
              {lang === 'zh' && <span className="hidden md:inline"> 妙意灵溪</span>}
            </span>
          </Link>
        </div>

        <div className="flex gap-3 items-center">
          {/* Navigation Links */}
          <div className="flex gap-3 items-center">
            {mainLinks.map((link) => (
              <Link
                key={link.key}
                href={link.href}
                className="hover:text-gray-400 text-[#d0d1df] text-sm flex items-center gap-1"
              >
                {link.icon && <link.icon className="h-4 w-4" />}
                {link.label}
              </Link>
            ))}
          </div>

          {/* Language Links - Static version */}
          <div className="flex gap-2">
            <Link
              href={`/en/blog/category/all`}
              className={`text-sm px-2 py-1 rounded ${lang === 'en' ? 'bg-primary text-primary-foreground' : 'text-[#d0d1df] hover:text-gray-400'}`}
            >
              EN
            </Link>
            <Link
              href={`/zh/blog/category/all`}
              className={`text-sm px-2 py-1 rounded ${lang === 'zh' ? 'bg-primary text-primary-foreground' : 'text-[#d0d1df] hover:text-gray-400'}`}
            >
              中文
            </Link>
          </div>
        </div>
      </div>
    </header>
  )
}
