import Link from 'next/link'
import Image from 'next/image'
import logo from '@/assets/mind-elixir-desktop.svg'
import { Footer } from '@/components/landing/Footer'

function Header() {
  return (
    <header className="border-b fixed w-screen top-0 left-0 z-100 bg-white dark:bg-darkblue">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex justify-between items-center text-gray-900 dark:text-white">
        <Link href="/" className="flex items-center">
          <Image src={logo} width={24} height={24} alt="MindElixir" />
          <span className="ml-2 text-xl font-semibold">MindElixir</span>
        </Link>
        <nav className="flex gap-6">
          <Link href="/" className="hover:text-gray-700">
            Home
          </Link>
          <Link href="/privacy" className="hover:text-gray-700">
            Privacy Policy
          </Link>
          <Link href="/terms" className="hover:text-gray-700">
            Terms of Service
          </Link>
        </nav>
      </div>
    </header>
  )
}

export default function LegalLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html
      lang="en"
      className="dark bg-white dark:bg-gray-950 text-black dark:text-white"
    >
      <body>
        <section className="flex flex-col min-h-screen">
          <Header />
          <div className="flex-grow pt-20">{children}</div>
          <Footer lang="en" />
        </section>
      </body>
    </html>
  )
}
