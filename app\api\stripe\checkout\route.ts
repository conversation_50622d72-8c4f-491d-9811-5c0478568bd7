import { NextRequest, NextResponse } from 'next/server'
import { stripe } from '@/lib/payments/stripe'
import Strip<PERSON> from 'stripe'
import { createPayment } from '@/lib/mongo/payment'
import { updateUserPayType } from '@/lib/mongo/user'
import { i18n } from '@/lib/i18n/config'

// Helper function to get locale from headers
function getLocaleFromHeaders(request: NextRequest): string {
  const acceptLanguage = request.headers.get('accept-language')

  if (!acceptLanguage) return i18n.defaultLocale

  // Simple language detection
  if (acceptLanguage.includes('zh')) return 'zh'
  return 'en'
}

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  const sessionId = searchParams.get('session_id')
  const locale = getLocaleFromHeaders(request)

  if (!sessionId) {
    return NextResponse.redirect(new URL(`/${locale}/pricing`, request.url))
  }
  debugger
  try {
    const session = await stripe.checkout.sessions.retrieve(sessionId)

    // const customerId = session.customer;
    const userUUID = session.client_reference_id
    if (!userUUID) {
      throw new Error("No user ID found in session's client_reference_id.")
    }
    // 订阅是 subscription
    const type = session.mode === 'payment' ? 'lifetime' : 'annual'
    await createPayment({
      userId: userUUID,
      customerId: session.customer as string,
      sessionId,
      subscriptionId: session.subscription as string,
      paymentIntent: session.payment_intent as string,
      type,
      createdAt: new Date(),
    })
    await updateUserPayType(userUUID, type)

    // await setSession(user[0]);
    return NextResponse.redirect(new URL(`/${locale}/profile`, request.url))
  } catch (error) {
    console.error('Error handling successful checkout:', error)
    return NextResponse.redirect(new URL(`/${locale}/error`, request.url))
  }
}
