{"private": true, "scripts": {"dev": "next dev --turbopack -p 4000", "build": "next build", "start": "next start", "db:setup": "npx tsx lib/db/setup.ts", "db:seed": "npx tsx lib/db/seed.ts", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@next/mdx": "^15.1.3", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-aspect-ratio": "^1.1.3", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.7", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.7", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.7", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.0", "@tailwindcss/postcss": "4.1.1", "@tailwindcss/typography": "^0.5.16", "@types/node": "^22.14.0", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "autoprefixer": "^10.4.21", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "drizzle-kit": "^0.30.6", "drizzle-orm": "^0.41.0", "embla-carousel-react": "^8.6.0", "gray-matter": "^4.0.3", "input-otp": "^1.4.2", "jose": "^6.0.10", "lucide-react": "^0.487.0", "mind-elixir": "5.0.0-beta.6", "mongodb": "^6.16.0", "motion": "^12.7.3", "next": "15.3.0-canary.31", "next-auth": "5.0.0-beta.27", "next-themes": "^0.4.6", "postcss": "^8.5.3", "postgres": "^3.4.5", "prismjs": "^1.29.0", "react": "19.1.0", "react-day-picker": "8.10.1", "react-dom": "19.1.0", "react-hook-form": "^7.55.0", "react-outliner-neo": "^0.1.0", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.2", "remark": "^15.0.1", "remark-html": "^16.0.1", "server-only": "^0.0.1", "sonner": "^2.0.3", "stripe": "^18.0.0", "tailwind-merge": "^3.1.0", "tailwindcss": "4.1.1", "tailwindcss-animate": "^1.0.7", "tailwindcss-react-aria-components": "2.0.0", "typescript": "^5.8.2", "vaul": "^1.1.2", "zod": "^3.24.2"}}