"use client";
import { useEffect, useState } from "react";
import { getCurrentUser } from "@/lib/get-session";
import { User } from "next-auth";
import SignOut from "./sign-out";
import { Button } from "./ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu";
import Link from "next/link";
import UserProfileAvatar from "./UserProfileAvatar";
import type { Locale } from "@/lib/i18n/config";
import type { Dictionary } from "@/types/i18n";

interface UserAvatarProps {
  lang: Locale;
  dict: Dictionary;
}

export default function UserAvatar({ lang, dict }: UserAvatarProps) {
  const [user, setUser] = useState<User & { type?: "annual" | "lifetime" }>();
  useEffect(() => {
    async function init() {
      const user = await getCurrentUser();
      setUser(user);
    }
    init();
  }, []);

  return user ? (
    <DropdownMenu modal={false}>
      <DropdownMenuTrigger>
        <UserProfileAvatar
          image={user?.image || ""}
          name={user?.name || "User"}
          type={user?.type}
          size="sm"
        />
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="z-100">
        <DropdownMenuItem
          className="cursor-pointer"
          onClick={() => (window.location.href = `/${lang}/profile`)}
        >
          {dict.common.profile}
        </DropdownMenuItem>
        <DropdownMenuItem className="cursor-pointer">
          <SignOut text={dict.common.signOut} />
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  ) : (
    <Button asChild>
      <Link href={`/${lang}/sign-in`}>{dict.common.signIn}</Link>
    </Button>
  );
}
