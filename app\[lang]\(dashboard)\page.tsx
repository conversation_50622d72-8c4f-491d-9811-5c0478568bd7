import { HeroSection } from '@/components/landing/hero-section';
import { FeatureBento } from '@/components/landing/feature-bento';
import { FaqSection } from '@/components/landing/faq-section';
import { FeatureSection } from '@/components/landing/feature-section';
import { Footer } from '@/components/landing/Footer';
import { PricingSection } from '@/components/landing/pricing-section';
import { AiSection } from '@/components/landing/ai-section';
import { DownloadSection } from '@/components/landing/download-section';
import type { PageProps } from '@/types/i18n';

export default async function HomePage({ params }: PageProps) {
  const { lang } = await params;

  return (
    <main>
      <HeroSection lang={lang} />
      <FeatureSection lang={lang} />
      <FeatureBento lang={lang} />
      <AiSection lang={lang} />
      <PricingSection lang={lang} />
      <DownloadSection lang={lang} />
      <FaqSection lang={lang} />
      <Footer lang={lang} />
    </main>
  );
}
