import { z } from 'zod'
// import { getTeamForUser, getUser } from '@/lib/db/queries'
import { redirect } from 'next/navigation'
import { getCurrentUser } from '../get-session'
import { User } from 'next-auth'
import Stripe from 'stripe'
import { MeUser } from '@/auth'
import { headers } from 'next/headers'
import { i18n } from '@/lib/i18n/config'

export type ActionState = {
  error?: string
  success?: string
  [key: string]: any // This allows for additional properties
}

type ValidatedActionFunction<S extends z.ZodType<any, any>, T> = (
  data: z.infer<S>,
  formData: FormData
) => Promise<T>

export function validatedAction<S extends z.ZodType<any, any>, T>(
  schema: S,
  action: ValidatedActionFunction<S, T>
) {
  return async (prevState: ActionState, formData: FormData): Promise<T> => {
    const result = schema.safeParse(Object.fromEntries(formData))
    if (!result.success) {
      return { error: result.error.errors[0].message } as T
    }

    return action(result.data, formData)
  }
}

// Helper function to get locale from headers
async function getLocaleFromHeaders() {
  const headersList = await headers()
  const acceptLanguage = headersList.get('accept-language')

  if (!acceptLanguage) return i18n.defaultLocale

  // Simple language detection
  if (acceptLanguage.includes('zh')) return 'zh'
  return 'en'
}

// Helper function to redirect to sign-in with proper locale
async function redirectToSignIn() {
  const locale = await getLocaleFromHeaders()
  redirect(`/${locale}/sign-in`)
}

type ActionWithTeamFunction<T> = (product: FormData, user: MeUser) => Promise<T>

export function withUser<T>(action: ActionWithTeamFunction<T>) {
  return async (product: FormData): Promise<T> => {
    const user = await getCurrentUser()
    if (!user) {
      await redirectToSignIn()
    }

    return action(product, user!)
  }
}
