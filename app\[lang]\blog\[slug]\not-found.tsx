import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft, FileX } from 'lucide-react';

export default function NotFound() {
  return (
    <div className="container mx-auto px-4 py-12">
      <div className="max-w-2xl mx-auto text-center">
        <div className="mb-8">
          <FileX className="h-24 w-24 mx-auto text-muted-foreground mb-4" />
          <h1 className="text-4xl font-bold mb-4">Blog Post Not Found</h1>
          <p className="text-xl text-muted-foreground mb-8">
            The blog post you're looking for doesn't exist or may have been moved.
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button asChild variant="default">
            <Link href="/en/blog/category/all">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Blog
            </Link>
          </Button>
          <Button asChild variant="outline">
            <Link href="/en">
              Go to Homepage
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
